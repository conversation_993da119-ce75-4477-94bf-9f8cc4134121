#[test_only]
module suitrump_dex::security_precision_tests {
    use sui::test_scenario::{Self as ts};
    use std::debug;
    use std::vector;
    use suitrump_dex::fixed_point_math::{Self as fp_math};

    const ADMIN: address = @0x1;
    const PRECISION: u256 = 1000000000000000000; // 1e18
    const MAX_U256: u256 = 115792089237316195423570985008687907853269984665640564039457584007913129639935;

    /// Test 1: Demonstrate precision loss in reward calculations
    #[test]
    fun test_reward_precision_loss_vulnerability() {
        let scenario = ts::begin(ADMIN);
        {
            debug::print(&b"=== TESTING REWARD PRECISION LOSS VULNERABILITY ===");
            
            // Simulate the problematic calculation pattern from farm contract:
            // reward_per_token_delta = fixed_point_math::div(
            //     fixed_point_math::new(reward_amount * PRECISION),  // Potential overflow
            //     fixed_point_math::new(pool.total_staked)
            // );
            
            debug::print(&b"Testing reward calculation with large numbers...");
            
            let reward_amount = 1000000000000000000u256; // 1 token (18 decimals)
            let total_staked = 999999999999999999999999u256; // Very large staked amount
            
            debug::print(&b"Reward amount:");
            debug::print(&reward_amount);
            debug::print(&b"Total staked:");
            debug::print(&total_staked);
            
            // This multiplication can overflow before being passed to fixed_point_math::new()
            let reward_scaled = reward_amount * PRECISION;
            debug::print(&b"Reward scaled (reward_amount * PRECISION):");
            debug::print(&reward_scaled);
            
            // Check if overflow occurred (avoid division by zero)
            if (PRECISION != 0 && reward_scaled / PRECISION != reward_amount) {
                debug::print(&b"VULNERABILITY: Overflow in reward_amount * PRECISION");
                debug::print(&b"This causes incorrect reward calculations");
            };
            
            // Even if no overflow, precision loss occurs in division
            let reward_per_token_fp = fp_math::div(
                fp_math::new(reward_scaled),
                fp_math::new(total_staked)
            );
            
            let reward_per_token_raw = fp_math::get_raw_value(reward_per_token_fp);
            debug::print(&b"Reward per token (raw fixed-point):");
            debug::print(&reward_per_token_raw);
            
            // When converting back to regular precision:
            let reward_per_token_final = reward_per_token_raw / PRECISION;
            debug::print(&b"Reward per token (final):");
            debug::print(&reward_per_token_final);
            
            if (reward_per_token_final == 0 && reward_per_token_raw > 0) {
                debug::print(&b"VULNERABILITY: Precision loss causes non-zero reward to become zero");
                debug::print(&b"Users lose their earned rewards");
            };
        };
        ts::end(scenario);
    }

    /// Test 2: Demonstrate cumulative precision loss attack
    #[test]
    fun test_cumulative_precision_loss_attack() {
        let scenario = ts::begin(ADMIN);
        {
            debug::print(&b"=== TESTING CUMULATIVE PRECISION LOSS ATTACK ===");
            
            // Demonstrate how small precision losses accumulate over time
            let mut accumulated_error = 0u256;
            let mut operation_count = 0;
            
            debug::print(&b"Simulating many small operations with precision loss...");
            
            while (operation_count < 1000) {
                // Simulate small division that causes precision loss
                let small_dividend = 1000u256;
                let large_divisor = 999999999u256;
                
                // Direct division (what should happen)
                let exact_result = small_dividend * PRECISION / large_divisor;
                
                // Fixed-point division (what actually happens)
                let fp_dividend = fp_math::new(small_dividend);
                let fp_divisor = fp_math::new(large_divisor);
                let fp_result = fp_math::div(fp_dividend, fp_divisor);
                let fp_result_raw = fp_math::get_raw_value(fp_result);
                
                // Calculate precision loss for this operation
                if (exact_result > fp_result_raw) {
                    let loss = exact_result - fp_result_raw;
                    accumulated_error = accumulated_error + loss;
                };
                
                operation_count = operation_count + 1;
            };
            
            debug::print(&b"Operations performed:");
            debug::print(&(operation_count as u256));
            debug::print(&b"Total accumulated precision error:");
            debug::print(&accumulated_error);
            debug::print(&b"Error as percentage of PRECISION:");
            debug::print(&(accumulated_error * 100 / PRECISION));
            
            if (accumulated_error > PRECISION / 1000) { // More than 0.1% error
                debug::print(&b"VULNERABILITY: Significant cumulative precision loss");
                debug::print(&b"This represents fund loss that accumulates over time");
            };
        };
        ts::end(scenario);
    }

    /// Test 3: Demonstrate division by zero and near-zero vulnerabilities
    #[test]
    fun test_division_by_near_zero_vulnerability() {
        let scenario = ts::begin(ADMIN);
        {
            debug::print(&b"=== TESTING DIVISION BY NEAR-ZERO VULNERABILITY ===");
            
            // Test division by very small numbers
            let normal_dividend = 1000000000000000000u256; // 1 token
            let tiny_divisor = 1u256; // Extremely small divisor
            
            debug::print(&b"Normal dividend:");
            debug::print(&normal_dividend);
            debug::print(&b"Tiny divisor:");
            debug::print(&tiny_divisor);
            
            let fp_dividend = fp_math::new(normal_dividend);
            let fp_divisor = fp_math::new(tiny_divisor);
            
            // This division will produce an extremely large result
            let fp_result = fp_math::div(fp_dividend, fp_divisor);
            let result_value = fp_math::get_raw_value(fp_result);
            
            debug::print(&b"Division result:");
            debug::print(&result_value);
            
            // Check if result is unreasonably large
            if (result_value > MAX_U256 / 1000) {
                debug::print(&b"VULNERABILITY: Division by tiny number produces huge result");
                debug::print(&b"This could be exploited to drain rewards or manipulate calculations");
            };
            
            // Test the reverse - tiny dividend, normal divisor
            let tiny_dividend = 1u256;
            let normal_divisor = 1000000000000000000u256;
            
            debug::print(&b"Tiny dividend:");
            debug::print(&tiny_dividend);
            debug::print(&b"Normal divisor:");
            debug::print(&normal_divisor);
            
            let fp_tiny = fp_math::new(tiny_dividend);
            let fp_normal = fp_math::new(normal_divisor);
            let fp_tiny_result = fp_math::div(fp_tiny, fp_normal);
            let tiny_result_value = fp_math::get_raw_value(fp_tiny_result);
            
            debug::print(&b"Tiny division result:");
            debug::print(&tiny_result_value);
            
            if (tiny_result_value == 0) {
                debug::print(&b"VULNERABILITY: Division result rounds down to zero");
                debug::print(&b"Non-zero values become zero, causing fund loss");
            };
        };
        ts::end(scenario);
    }

    /// Test 4: Demonstrate multiplication overflow in intermediate calculations
    #[test]
    fun test_intermediate_multiplication_overflow() {
        let scenario = ts::begin(ADMIN);
        {
            debug::print(&b"=== TESTING INTERMEDIATE MULTIPLICATION OVERFLOW ===");
            
            // Test the problematic pattern: (a_low * b_low) before division
            let large_a = MAX_U256 / 3; // Large but not max
            let large_b = MAX_U256 / 3; // Large but not max
            
            debug::print(&b"Large value A:");
            debug::print(&large_a);
            debug::print(&b"Large value B:");
            debug::print(&large_b);
            
            // Check if intermediate multiplication would overflow
            // This simulates the calculation in fixed_point_math::mul()
            let would_overflow = large_a > MAX_U256 / large_b;

            debug::print(&b"Would intermediate multiplication overflow?");
            debug::print(&(if (would_overflow) { 1u256 } else { 0u256 }));
            
            if (would_overflow) {
                debug::print(&b"VULNERABILITY: Intermediate multiplication overflows");
                debug::print(&b"Even though individual values are valid, multiplication fails");
                debug::print(&b"This can cause swaps and LP operations to fail unexpectedly");
            };
            
            // Test the shift operation overflow: (value << 64)
            let shift_test_value = MAX_U256 >> 63; // Value that will overflow when shifted left 64
            debug::print(&b"Shift test value:");
            debug::print(&shift_test_value);
            
            let would_shift_overflow = shift_test_value > (MAX_U256 >> 64);
            debug::print(&b"Would shift operation overflow?");
            debug::print(&(if (would_shift_overflow) { 1u256 } else { 0u256 }));
            
            if (would_shift_overflow) {
                debug::print(&b"VULNERABILITY: Shift operation << 64 causes overflow");
                debug::print(&b"This affects the high-precision multiplication in fixed_point_math");
            };
        };
        ts::end(scenario);
    }

    /// Test 5: Demonstrate scaling precision loss vulnerability
    #[test]
    fun test_scaling_precision_loss_vulnerability() {
        let scenario = ts::begin(ADMIN);
        {
            debug::print(&b"=== TESTING SCALING PRECISION LOSS VULNERABILITY ===");
            
            // Test precision loss when scaling between different decimal places
            let value_6_decimals = 1000000u256; // 1 token with 6 decimals
            let _value_9_decimals = 1000000000u256; // 1 token with 9 decimals
            let value_18_decimals = 1000000000000000000u256; // 1 token with 18 decimals
            
            debug::print(&b"Testing scaling from 6 decimals to 18 decimals...");
            
            // Scale up from 6 to 18 decimals (multiply by 10^12)
            let scale_factor = 1000000000000u256; // 10^12
            let scaled_up = value_6_decimals * scale_factor;
            
            debug::print(&b"Original (6 decimals):");
            debug::print(&value_6_decimals);
            debug::print(&b"Scaled up (18 decimals):");
            debug::print(&scaled_up);
            debug::print(&b"Expected (18 decimals):");
            debug::print(&value_18_decimals);
            
            // Should be equal
            if (scaled_up != value_18_decimals) {
                debug::print(&b"VULNERABILITY: Scaling calculation error");
            };
            
            debug::print(&b"Testing scaling down from 18 decimals to 6 decimals...");
            
            // Scale down from 18 to 6 decimals (divide by 10^12)
            let scaled_down = value_18_decimals / scale_factor;
            
            debug::print(&b"Original (18 decimals):");
            debug::print(&value_18_decimals);
            debug::print(&b"Scaled down (6 decimals):");
            debug::print(&scaled_down);
            debug::print(&b"Expected (6 decimals):");
            debug::print(&value_6_decimals);
            
            if (scaled_down != value_6_decimals) {
                debug::print(&b"VULNERABILITY: Scaling down calculation error");
            };
            
            // Test precision loss with non-exact scaling
            let imprecise_value = 1000001u256; // 1.000001 tokens with 6 decimals
            let scaled_up_imprecise = imprecise_value * scale_factor;
            let scaled_back_down = scaled_up_imprecise / scale_factor;
            
            debug::print(&b"Testing precision loss with imprecise value...");
            debug::print(&b"Original imprecise value:");
            debug::print(&imprecise_value);
            debug::print(&b"After scaling up and down:");
            debug::print(&scaled_back_down);
            
            if (scaled_back_down != imprecise_value) {
                let precision_loss = if (imprecise_value > scaled_back_down) {
                    imprecise_value - scaled_back_down
                } else {
                    scaled_back_down - imprecise_value
                };
                debug::print(&b"VULNERABILITY: Precision loss in scaling:");
                debug::print(&precision_loss);
            };
        };
        ts::end(scenario);
    }

    /// Test 6: Demonstrate square root precision vulnerabilities
    #[test]
    fun test_sqrt_precision_vulnerability() {
        let scenario = ts::begin(ADMIN);
        {
            debug::print(&b"=== TESTING SQRT PRECISION VULNERABILITY ===");
            
            // Test sqrt with values that cause precision issues
            let test_values = vector[
                1u256,                    // Minimum value
                999u256,                  // Just under 1000
                1000u256,                 // Round number
                1001u256,                 // Just over 1000
                PRECISION,                // 1.0 in fixed point
                PRECISION + 1,            // Just over 1.0
                MAX_U256 / 1000000,       // Large value
            ];

            let mut i = 0;
            while (i < 7) {
                let test_value = test_values[i];
                
                debug::print(&b"Testing sqrt of:");
                debug::print(&test_value);
                
                let fp_value = fp_math::new(test_value);
                let fp_sqrt = fp_math::sqrt(fp_value);
                let sqrt_result = fp_math::get_raw_value(fp_sqrt);
                
                debug::print(&b"Sqrt result:");
                debug::print(&sqrt_result);
                
                // Verify by squaring the result
                let fp_squared = fp_math::mul(fp_sqrt, fp_sqrt);
                let squared_result = fp_math::get_raw_value(fp_squared);
                
                debug::print(&b"Squared back:");
                debug::print(&squared_result);
                
                // Check precision loss
                if (squared_result != test_value) {
                    let precision_error = if (squared_result > test_value) {
                        squared_result - test_value
                    } else {
                        test_value - squared_result
                    };
                    debug::print(&b"VULNERABILITY: Sqrt precision loss:");
                    debug::print(&precision_error);
                    
                    // Calculate error percentage
                    let error_percentage = precision_error * 100 / test_value;
                    debug::print(&b"Error percentage:");
                    debug::print(&error_percentage);
                    
                    if (error_percentage > 1) { // More than 1% error
                        debug::print(&b"CRITICAL: Sqrt precision error > 1%");
                    };
                };
                
                i = i + 1;
            };
        };
        ts::end(scenario);
    }
}
