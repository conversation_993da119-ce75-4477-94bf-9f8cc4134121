#[test_only]
module suitrump_dex::security_fund_loss_tests {
    use sui::test_scenario::{Self as ts, <PERSON><PERSON><PERSON>};
    use sui::coin::{Self, mint_for_testing, Coin};
    use std::string::utf8;
    use std::option;
    use std::debug;
    use suitrump_dex::pair::{Self, AdminCap, Pair, LPCoin};
    use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
    use suitrump_dex::test_coins::{Self, USDC};
    use suitrump_dex::victory_token::{Self, VICTORY_TOKEN, TreasuryCapWrapper};
    use suitrump_dex::global_emission_controller::{Self, GlobalEmissionConfig, AdminCap as EmissionAdminCap};
    use suitrump_dex::fixed_point_math::{Self as fp_math};
    use sui::clock::{Self, Clock};
    use sui::transfer;

    const ADMIN: address = @0x1;
    const USER1: address = @0x2;
    const USER2: address = @0x3;
    const ATTACKER: address = @0x666;
    const TEAM_1: address = @0x44;
    const TEAM_2: address = @0x45;
    const DEV: address = @0x46;
    const LOCKER: address = @0x47;
    const BUYBACK: address = @0x48;

    const INITIAL_LIQUIDITY: u64 = 1_000_000_000_000; // Large initial liquidity
    const PRECISION: u256 = 1000000000000000000; // 1e18

    fun setup_pair_with_liquidity(scenario: &mut Scenario) {
        ts::next_tx(scenario, ADMIN);
        {
            pair::init_for_testing(ts::ctx(scenario));
        };

        ts::next_tx(scenario, ADMIN);
        {
            let cap = ts::take_from_sender<AdminCap>(scenario);
            let pair = pair::new<sui::sui::SUI, USDC>(
                utf8(b"SUI"),
                utf8(b"USDC"),
                TEAM_1,
                TEAM_2,
                DEV,
                LOCKER,
                BUYBACK,
                ts::ctx(scenario)
            );
            pair::share_pair(pair);
            ts::return_to_sender(scenario, cap);
        };

        // Add large initial liquidity
        ts::next_tx(scenario, ADMIN);
        {
            let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(scenario);
            let coin0 = mint_for_testing<sui::sui::SUI>(INITIAL_LIQUIDITY, ts::ctx(scenario));
            let coin1 = mint_for_testing<USDC>(INITIAL_LIQUIDITY, ts::ctx(scenario));
            
            let lp_tokens = pair::mint(&mut pair, coin0, coin1, ts::ctx(scenario));
            transfer::public_transfer(lp_tokens, ADMIN);
            ts::return_shared(pair);
        };
    }

    /// Test 1: Demonstrate precision loss leading to fund loss
    #[test]
    fun test_precision_loss_fund_drain() {
        let mut scenario = ts::begin(ADMIN);
        setup_pair_with_liquidity(&mut scenario);

        debug::print(&b"=== TESTING PRECISION LOSS FUND DRAIN ===");

        ts::next_tx(&mut scenario, ATTACKER);
        {
            let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);
            
            debug::print(&b"Demonstrating precision loss attack...");
            
            // Get initial reserves
            let (reserve0_initial, reserve1_initial, _) = pair::get_reserves(&pair);
            debug::print(&b"Initial reserves:");
            debug::print(&reserve0_initial);
            debug::print(&reserve1_initial);

            // Perform many small swaps to accumulate precision loss
            let mut swap_count = 0;
            let small_swap_amount = 1u64; // Extremely small swap
            
            while (swap_count < 100) {
                let swap_in = mint_for_testing<sui::sui::SUI>(small_swap_amount, ts::ctx(&mut scenario));
                
                // Small swaps cause precision loss in calculations
                let (coin0_out, coin1_out) = pair::swap(
                    &mut pair,
                    option::some(swap_in),
                    option::none(),
                    0,
                    0, // Let the pair calculate output
                    ts::ctx(&mut scenario)
                );

                // Clean up outputs
                if (option::is_some(&coin0_out)) {
                    let coin = option::destroy_some(coin0_out);
                    coin::burn_for_testing(coin);
                } else {
                    option::destroy_none(coin0_out);
                };
                
                if (option::is_some(&coin1_out)) {
                    let coin = option::destroy_some(coin1_out);
                    coin::burn_for_testing(coin);
                } else {
                    option::destroy_none(coin1_out);
                };

                swap_count = swap_count + 1;
            };

            // Check reserves after precision loss accumulation
            let (reserve0_final, reserve1_final, _) = pair::get_reserves(&pair);
            debug::print(&b"Final reserves after precision loss:");
            debug::print(&reserve0_final);
            debug::print(&reserve1_final);

            // Calculate total value lost due to precision errors
            let initial_k = reserve0_initial * reserve1_initial;
            let final_k = reserve0_final * reserve1_final;
            
            debug::print(&b"Initial K:");
            debug::print(&initial_k);
            debug::print(&b"Final K:");
            debug::print(&final_k);

            if (final_k < initial_k) {
                let k_loss = initial_k - final_k;
                debug::print(&b"VULNERABILITY: K invariant decreased due to precision loss");
                debug::print(&b"K loss amount:");
                debug::print(&k_loss);
                debug::print(&b"This represents permanent fund loss");
            };

            ts::return_shared(pair);
        };
        ts::end(scenario);
    }

    /// Test 2: Demonstrate LP token calculation errors leading to fund loss
    #[test]
    fun test_lp_token_calculation_fund_loss() {
        let mut scenario = ts::begin(ADMIN);
        setup_pair_with_liquidity(&mut scenario);

        debug::print(&b"=== TESTING LP TOKEN CALCULATION FUND LOSS ===");

        ts::next_tx(&mut scenario, USER1);
        {
            let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);
            
            debug::print(&b"Testing LP token minting with extreme ratios...");
            
            // Get current reserves
            let (reserve0, reserve1, _) = pair::get_reserves(&pair);
            let total_supply = pair::total_supply(&pair);
            
            debug::print(&b"Current state:");
            debug::print(&b"Reserve0:");
            debug::print(&reserve0);
            debug::print(&b"Reserve1:");
            debug::print(&reserve1);
            debug::print(&b"Total supply:");
            debug::print(&total_supply);

            // Add liquidity with extreme ratio (very small amount)
            let tiny_amount0 = 1u64;
            let tiny_amount1 = 1u64;
            
            let coin0 = mint_for_testing<sui::sui::SUI>(tiny_amount0, ts::ctx(&mut scenario));
            let coin1 = mint_for_testing<USDC>(tiny_amount1, ts::ctx(&mut scenario));
            
            debug::print(&b"Adding tiny liquidity amounts:");
            debug::print(&(tiny_amount0 as u256));
            debug::print(&(tiny_amount1 as u256));
            
            // This should result in very few or zero LP tokens due to precision loss
            let lp_tokens = pair::mint(&mut pair, coin0, coin1, ts::ctx(&mut scenario));
            let lp_amount = coin::value(&lp_tokens);
            
            debug::print(&b"LP tokens received:");
            debug::print(&(lp_amount as u256));
            
            if (lp_amount == 0) {
                debug::print(&b"VULNERABILITY: User provided liquidity but received 0 LP tokens");
                debug::print(&b"This is permanent fund loss - user loses their tokens");
            } else if (lp_amount < (tiny_amount0 + tiny_amount1)) {
                debug::print(&b"VULNERABILITY: LP tokens received less than input value");
                debug::print(&b"User loses value due to precision errors");
            };

            transfer::public_transfer(lp_tokens, USER1);
            ts::return_shared(pair);
        };
        ts::end(scenario);
    }

    /// Test 3: Demonstrate reward calculation errors in farming
    #[test]
    fun test_reward_calculation_fund_loss() {
        let mut scenario = ts::begin(ADMIN);

        debug::print(&b"=== TESTING REWARD CALCULATION FUND LOSS ===");

        // This test demonstrates how precision loss in reward calculations
        // can lead to users losing their rightful rewards
        
        ts::next_tx(&mut scenario, USER1);
        {
            debug::print(&b"Simulating reward calculation precision loss...");
            
            // Simulate the problematic calculation from the farm contract:
            // pending_rewards = fixed_point_math::get_raw_value(pending) / PRECISION;
            
            let large_reward_amount = 1000000000000000000u256; // 1 token with 18 decimals
            let large_total_staked = 999999999999999999999u256; // Very large staked amount
            
            debug::print(&b"Reward amount:");
            debug::print(&large_reward_amount);
            debug::print(&b"Total staked:");
            debug::print(&large_total_staked);
            
            // Calculate reward per token (this is where precision loss occurs)
            let reward_per_token_fp = fp_math::div(
                fp_math::new(large_reward_amount * PRECISION), // This can overflow!
                fp_math::new(large_total_staked)
            );
            
            let reward_per_token_raw = fp_math::get_raw_value(reward_per_token_fp);
            debug::print(&b"Reward per token (raw):");
            debug::print(&reward_per_token_raw);
            
            // When user claims with small stake
            let small_user_stake = 1000u256;
            let user_reward_fp = fp_math::mul_small(reward_per_token_fp, (small_user_stake as u64));
            let user_reward_raw = fp_math::get_raw_value(user_reward_fp);
            let user_reward_final = user_reward_raw / PRECISION; // Precision loss here!
            
            debug::print(&b"User stake:");
            debug::print(&small_user_stake);
            debug::print(&b"User reward (before precision loss):");
            debug::print(&user_reward_raw);
            debug::print(&b"User reward (after precision loss):");
            debug::print(&user_reward_final);
            
            if (user_reward_final == 0 && user_reward_raw > 0) {
                debug::print(&b"VULNERABILITY: User earned rewards but receives 0 due to precision loss");
                debug::print(&b"This is fund loss - user loses their earned rewards");
            };
            
            // Demonstrate cumulative effect
            debug::print(&b"Over many users, this precision loss accumulates");
            debug::print(&b"Protocol keeps the 'dust' that should go to users");
        };
        ts::end(scenario);
    }

    /// Test 4: Demonstrate minimum liquidity attack
    #[test]
    fun test_minimum_liquidity_attack() {
        let mut scenario = ts::begin(ADMIN);

        debug::print(&b"=== TESTING MINIMUM LIQUIDITY ATTACK ===");

        ts::next_tx(&mut scenario, ADMIN);
        {
            pair::init_for_testing(ts::ctx(&mut scenario));
        };

        ts::next_tx(&mut scenario, ADMIN);
        {
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            let pair = pair::new<sui::sui::SUI, USDC>(
                utf8(b"SUI"),
                utf8(b"USDC"),
                TEAM_1,
                TEAM_2,
                DEV,
                LOCKER,
                BUYBACK,
                ts::ctx(&mut scenario)
            );
            pair::share_pair(pair);
            ts::return_to_sender(&scenario, cap);
        };

        ts::next_tx(&mut scenario, ATTACKER);
        {
            let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);
            
            debug::print(&b"Attacker performing minimum liquidity attack...");
            
            // Attacker adds minimal liquidity to manipulate the pool
            let minimal_amount0 = 1u64;
            let minimal_amount1 = 1u64;
            
            let coin0 = mint_for_testing<sui::sui::SUI>(minimal_amount0, ts::ctx(&mut scenario));
            let coin1 = mint_for_testing<USDC>(minimal_amount1, ts::ctx(&mut scenario));
            
            debug::print(&b"Adding minimal liquidity:");
            debug::print(&(minimal_amount0 as u256));
            debug::print(&(minimal_amount1 as u256));
            
            let lp_tokens = pair::mint(&mut pair, coin0, coin1, ts::ctx(&mut scenario));
            let lp_amount = coin::value(&lp_tokens);
            
            debug::print(&b"LP tokens received:");
            debug::print(&(lp_amount as u256));
            
            // Now when legitimate users add liquidity, they get unfavorable rates
            transfer::public_transfer(lp_tokens, ATTACKER);
            ts::return_shared(pair);
        };

        // Legitimate user adds liquidity
        ts::next_tx(&mut scenario, USER1);
        {
            let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);
            
            let normal_amount0 = 1000000u64;
            let normal_amount1 = 1000000u64;
            
            let coin0 = mint_for_testing<sui::sui::SUI>(normal_amount0, ts::ctx(&mut scenario));
            let coin1 = mint_for_testing<USDC>(normal_amount1, ts::ctx(&mut scenario));
            
            debug::print(&b"Legitimate user adding normal liquidity:");
            debug::print(&(normal_amount0 as u256));
            debug::print(&(normal_amount1 as u256));
            
            let lp_tokens = pair::mint(&mut pair, coin0, coin1, ts::ctx(&mut scenario));
            let lp_amount = coin::value(&lp_tokens);
            
            debug::print(&b"LP tokens received by legitimate user:");
            debug::print(&(lp_amount as u256));
            
            // Check if user got fair LP tokens
            let expected_fair_share = (normal_amount0 + normal_amount1) as u256;
            if ((lp_amount as u256) < expected_fair_share / 2) {
                debug::print(&b"VULNERABILITY: User received unfairly few LP tokens");
                debug::print(&b"Attacker's minimal liquidity manipulated the pool");
                debug::print(&b"User loses value due to unfavorable LP token rate");
            };

            transfer::public_transfer(lp_tokens, USER1);
            ts::return_shared(pair);
        };

        ts::end(scenario);
    }

    /// Test 5: Demonstrate K invariant violation fund loss
    #[test]
    fun test_k_invariant_violation_fund_loss() {
        let mut scenario = ts::begin(ADMIN);
        setup_pair_with_liquidity(&mut scenario);

        debug::print(&b"=== TESTING K INVARIANT VIOLATION FUND LOSS ===");

        ts::next_tx(&mut scenario, ATTACKER);
        {
            let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);
            
            debug::print(&b"Testing K invariant violations...");
            
            // Get initial K value
            let (reserve0_initial, reserve1_initial, _) = pair::get_reserves(&pair);
            let k_initial = reserve0_initial * reserve1_initial;
            
            debug::print(&b"Initial K value:");
            debug::print(&k_initial);
            
            // Perform a series of operations that could violate K invariant
            // due to precision loss and rounding errors
            let mut operation_count = 0;
            
            while (operation_count < 50) {
                // Small swap that causes rounding errors
                let small_swap = mint_for_testing<sui::sui::SUI>(1u64, ts::ctx(&mut scenario));
                
                let (coin0_out, coin1_out) = pair::swap(
                    &mut pair,
                    option::some(small_swap),
                    option::none(),
                    0,
                    0,
                    ts::ctx(&mut scenario)
                );

                // Clean up
                if (option::is_some(&coin0_out)) {
                    let coin = option::destroy_some(coin0_out);
                    coin::burn_for_testing(coin);
                } else {
                    option::destroy_none(coin0_out);
                };
                
                if (option::is_some(&coin1_out)) {
                    let coin = option::destroy_some(coin1_out);
                    coin::burn_for_testing(coin);
                } else {
                    option::destroy_none(coin1_out);
                };

                operation_count = operation_count + 1;
            };
            
            // Check final K value
            let (reserve0_final, reserve1_final, _) = pair::get_reserves(&pair);
            let k_final = reserve0_final * reserve1_final;
            
            debug::print(&b"Final K value:");
            debug::print(&k_final);
            
            if (k_final < k_initial) {
                let k_loss = k_initial - k_final;
                debug::print(&b"VULNERABILITY: K invariant violated - value lost");
                debug::print(&b"K loss:");
                debug::print(&k_loss);
                debug::print(&b"This represents permanent fund loss from the pool");
            };

            ts::return_shared(pair);
        };
        ts::end(scenario);
    }
}
