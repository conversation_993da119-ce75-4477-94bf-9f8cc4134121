#[test_only]
module suitrump_dex::security_reentrancy_tests {
    use sui::test_scenario::{Self as ts, <PERSON><PERSON><PERSON>};
    use sui::coin::{Self, mint_for_testing, Coin};
    use sui::test_utils::assert_eq;
    use std::string::utf8;
    use std::option;
    use std::debug;
    use suitrump_dex::pair::{Self, AdminCap, Pair, LPCoin};
    use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
    use suitrump_dex::test_coins::{Self, USDC};
    use suitrump_dex::victory_token::{Self, VICTORY_TOKEN, TreasuryCapWrapper};
    use suitrump_dex::global_emission_controller::{Self, GlobalEmissionConfig, AdminCap as EmissionAdminCap};
    use sui::clock::{Self, Clock};
    use sui::transfer;

    const ADMIN: address = @0x1;
    const ATTACKER: address = @0x666;
    const TEAM_1: address = @0x44;
    const TEAM_2: address = @0x45;
    const DEV: address = @0x46;
    const LOCKER: address = @0x47;
    const BUYBACK: address = @0x48;

    const INITIAL_LIQUIDITY: u64 = 1_000_000_000;
    const ATTACK_AMOUNT: u64 = 100_000_000;

    fun setup_pair(scenario: &mut Scenario) {
        ts::next_tx(scenario, ADMIN);
        {
            pair::init_for_testing(ts::ctx(scenario));
        };

        ts::next_tx(scenario, ADMIN);
        {
            let cap = ts::take_from_sender<AdminCap>(scenario);
            let pair = pair::new<sui::sui::SUI, USDC>(
                utf8(b"SUI"),
                utf8(b"USDC"),
                TEAM_1,
                TEAM_2,
                DEV,
                LOCKER,
                BUYBACK,
                ts::ctx(scenario)
            );
            pair::share_pair(pair);
            ts::return_to_sender(scenario, cap);
        };

        // Add initial liquidity
        ts::next_tx(scenario, ADMIN);
        {
            let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(scenario);
            let coin0 = mint_for_testing<sui::sui::SUI>(INITIAL_LIQUIDITY, ts::ctx(scenario));
            let coin1 = mint_for_testing<USDC>(INITIAL_LIQUIDITY, ts::ctx(scenario));
            
            let lp_tokens = pair::mint(&mut pair, coin0, coin1, ts::ctx(scenario));
            transfer::public_transfer(lp_tokens, ADMIN);
            ts::return_shared(pair);
        };
    }

    /// Test 1: Demonstrate swap reentrancy vulnerability
    /// This test shows how the K invariant can be violated during reentrancy
    #[test]
    fun test_swap_reentrancy_vulnerability() {
        let mut scenario = ts::begin(ADMIN);
        setup_pair(&mut scenario);

        debug::print(&b"=== TESTING SWAP REENTRANCY VULNERABILITY ===");

        ts::next_tx(&mut scenario, ATTACKER);
        {
            let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);
            
            // Get initial reserves
            let (reserve0_before, reserve1_before, _) = pair::get_reserves(&pair);
            debug::print(&b"Initial reserves:");
            debug::print(&reserve0_before);
            debug::print(&reserve1_before);

            // Perform a swap that will trigger fee transfers
            let swap_in = mint_for_testing<sui::sui::SUI>(ATTACK_AMOUNT, ts::ctx(&mut scenario));
            
            debug::print(&b"Performing swap with potential reentrancy window...");
            
            // This swap will:
            // 1. Take tokens from pair balance
            // 2. Transfer fees to external addresses (potential reentrancy point)
            // 3. Only THEN verify K invariant
            let (coin0_out, coin1_out) = pair::swap(
                &mut pair,
                option::some(swap_in),
                option::none(),
                0,
                1000, // Small amount out
                ts::ctx(&mut scenario)
            );

            // Get reserves after swap
            let (reserve0_after, reserve1_after, _) = pair::get_reserves(&pair);
            debug::print(&b"Reserves after swap:");
            debug::print(&reserve0_after);
            debug::print(&reserve1_after);

            // Calculate K values
            let k_before = reserve0_before * reserve1_before;
            let k_after = reserve0_after * reserve1_after;
            
            debug::print(&b"K before swap:");
            debug::print(&k_before);
            debug::print(&b"K after swap:");
            debug::print(&k_after);

            // In a reentrancy attack, an attacker could manipulate the state
            // between token withdrawal and K validation
            if (k_after < k_before) {
                debug::print(&b"VULNERABILITY: K invariant decreased during swap!");
                debug::print(&b"This indicates potential for reentrancy exploitation");
            };

            // Clean up
            if (option::is_some(&coin0_out)) {
                let coin = option::destroy_some(coin0_out);
                transfer::public_transfer(coin, ATTACKER);
            } else {
                option::destroy_none(coin0_out);
            };
            
            if (option::is_some(&coin1_out)) {
                let coin = option::destroy_some(coin1_out);
                transfer::public_transfer(coin, ATTACKER);
            } else {
                option::destroy_none(coin1_out);
            };

            ts::return_shared(pair);
        };
        ts::end(scenario);
    }

    /// Test 2: Demonstrate fee transfer reentrancy vulnerability
    #[test]
    fun test_fee_transfer_reentrancy_vulnerability() {
        let mut scenario = ts::begin(ADMIN);
        setup_pair(&mut scenario);

        debug::print(&b"=== TESTING FEE TRANSFER REENTRANCY VULNERABILITY ===");

        ts::next_tx(&mut scenario, ATTACKER);
        {
            let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);
            
            debug::print(&b"Testing multiple external transfers during fee distribution...");
            
            // Perform multiple swaps to trigger multiple fee transfers
            let mut swap_count = 0;
            while (swap_count < 3) {
                let swap_in = mint_for_testing<sui::sui::SUI>(ATTACK_AMOUNT / 3, ts::ctx(&mut scenario));
                
                debug::print(&b"Swap number:");
                debug::print(&(swap_count as u256));
                
                // Each swap triggers multiple external transfers:
                // - team_1_address (40% of team fee)
                // - team_2_address (50% of team fee) 
                // - dev_address (10% of team fee)
                // - locker_address
                // - buyback_address
                // This creates multiple reentrancy opportunities
                let (coin0_out, coin1_out) = pair::swap(
                    &mut pair,
                    option::some(swap_in),
                    option::none(),
                    0,
                    100, // Small amount out
                    ts::ctx(&mut scenario)
                );

                // Clean up outputs
                if (option::is_some(&coin0_out)) {
                    let coin = option::destroy_some(coin0_out);
                    transfer::public_transfer(coin, ATTACKER);
                } else {
                    option::destroy_none(coin0_out);
                };
                
                if (option::is_some(&coin1_out)) {
                    let coin = option::destroy_some(coin1_out);
                    transfer::public_transfer(coin, ATTACKER);
                } else {
                    option::destroy_none(coin1_out);
                };

                swap_count = swap_count + 1;
            };

            debug::print(&b"VULNERABILITY: Multiple external calls during fee distribution");
            debug::print(&b"Each transfer to external address is a potential reentrancy point");
            debug::print(&b"Attacker could re-enter during any of these transfers");

            ts::return_shared(pair);
        };
        ts::end(scenario);
    }

    /// Test 3: Demonstrate state inconsistency during external calls
    #[test]
    fun test_state_inconsistency_vulnerability() {
        let mut scenario = ts::begin(ADMIN);
        setup_pair(&mut scenario);

        debug::print(&b"=== TESTING STATE INCONSISTENCY VULNERABILITY ===");

        ts::next_tx(&mut scenario, ATTACKER);
        {
            let mut pair = ts::take_shared<Pair<sui::sui::SUI, USDC>>(&scenario);
            
            // Get initial state
            let (reserve0_initial, reserve1_initial, _) = pair::get_reserves(&pair);
            let total_supply_initial = pair::total_supply(&pair);
            
            debug::print(&b"Initial state:");
            debug::print(&b"Reserve0:");
            debug::print(&reserve0_initial);
            debug::print(&b"Reserve1:");
            debug::print(&reserve1_initial);
            debug::print(&b"Total supply:");
            debug::print(&total_supply_initial);

            // Perform swap that modifies state during external calls
            let swap_in = mint_for_testing<sui::sui::SUI>(ATTACK_AMOUNT, ts::ctx(&mut scenario));
            
            debug::print(&b"Performing swap that creates state inconsistency window...");
            
            // During this swap:
            // 1. Pair balances are modified (tokens taken out)
            // 2. External transfers happen (reentrancy window)
            // 3. Reserves are updated
            // 4. K invariant is checked
            // Between steps 1-2 and 3-4, the pair is in an inconsistent state
            let (coin0_out, coin1_out) = pair::swap(
                &mut pair,
                option::some(swap_in),
                option::none(),
                0,
                1000,
                ts::ctx(&mut scenario)
            );

            debug::print(&b"VULNERABILITY: State inconsistency window exists");
            debug::print(&b"Between token withdrawal and reserve update,");
            debug::print(&b"the pair state is inconsistent and exploitable");

            // Clean up
            if (option::is_some(&coin0_out)) {
                let coin = option::destroy_some(coin0_out);
                transfer::public_transfer(coin, ATTACKER);
            } else {
                option::destroy_none(coin0_out);
            };
            
            if (option::is_some(&coin1_out)) {
                let coin = option::destroy_some(coin1_out);
                transfer::public_transfer(coin, ATTACKER);
            } else {
                option::destroy_none(coin1_out);
            };

            ts::return_shared(pair);
        };
        ts::end(scenario);
    }
}
