arus<PERSON>v@DESKTOP-H0OO4JE:/mnt/c/Development/move/source-code/suidex_contract$ sui move test
[note] Dependencies on Bridge, MoveStdlib, Sui, and SuiSystem are automatically added, but this feature is disabled for your package because you have explicitly included dependencies on Sui. Consider removing these dependencies from Move.toml.
Cloning into '/home/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__testnet'...
remote: Enumerating objects: 31295, done.
remote: Counting objects: 100% (22/22), done.
remote: Compressing objects: 100% (21/21), done.
remote: Total 31295 (delta 1), reused 6 (delta 1), pack-reused 31273 (from 2)
Receiving objects: 100% (31295/31295), 16.32 MiB | 15.07 MiB/s, done.
Resolving deltas: 100% (3217/3217), done.
INCLUDING DEPENDENCY Sui
INCLUDING DEPENDENCY MoveStdlib
BUILDING suitrump_dex
warning[W09001]: unused alias
  ┌─ ./sources/library.move:3:21
  │
3 │     use sui::coin::{Self, Coin};
  │                     ^^^^ Unused 'use' of alias 'coin'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/library.move:3:27
  │
3 │     use sui::coin::{Self, Coin};
  │                           ^^^^ Unused 'use' of alias 'Coin'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/library.move:4:26
  │
4 │     use sui::tx_context::TxContext;
  │                          ^^^^^^^^^ Unnecessary alias 'TxContext' for module member 'sui::tx_context::TxContext'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/library.move:4:26
  │
4 │     use sui::tx_context::TxContext;
  │                          ^^^^^^^^^ Unused 'use' of alias 'TxContext'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/library.move:5:24
  │
5 │     use sui::balance::{Self, Balance};
  │                        ^^^^ Unused 'use' of alias 'balance'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/library.move:5:30
  │
5 │     use sui::balance::{Self, Balance};
  │                              ^^^^^^^ Unused 'use' of alias 'Balance'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/library.move:6:33
  │
6 │     use suitrump_dex::factory::{Self, Factory, TokenPair};
  │                                 ^^^^ Unused 'use' of alias 'factory'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/library.move:6:48
  │
6 │     use suitrump_dex::factory::{Self, Factory, TokenPair};
  │                                                ^^^^^^^^^ Unused 'use' of alias 'TokenPair'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/library.move:8:23
  │
8 │     use std::option::{Self, Option};
  │                       ^^^^ Unnecessary alias 'option' for module 'std::option'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/library.move:8:23
  │
8 │     use std::option::{Self, Option};
  │                       ^^^^ Unused 'use' of alias 'option'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/library.move:8:29
  │
8 │     use std::option::{Self, Option};
  │                             ^^^^^^ Unnecessary alias 'Option' for module member 'std::option::Option'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/library.move:8:29
  │
8 │     use std::option::{Self, Option};
  │                             ^^^^^^ Unused 'use' of alias 'Option'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/library.move:9:48
  │
9 │     use suitrump_dex::fixed_point_math::{Self, FixedPoint};
  │                                                ^^^^^^^^^^ Unused 'use' of alias 'FixedPoint'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/library.move:14:11
   │
14 │     const ERR_INVALID_PATH: u64 = 203;
   │           ^^^^^^^^^^^^^^^^ The constant 'ERR_INVALID_PATH' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/library.move:26:11
   │
26 │     const PRECISION: u256 = 1_000_000_000; // 1e9
   │           ^^^^^^^^^ The constant 'PRECISION' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_fund_loss_tests.move:4:45
  │
4 │     use sui::coin::{Self, mint_for_testing, Coin};
  │                                             ^^^^ Unused 'use' of alias 'Coin'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./tests/security_fund_loss_tests.move:6:14
  │
6 │     use std::option;
  │              ^^^^^^ Unnecessary alias 'option' for module 'std::option'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_fund_loss_tests.move:8:52
  │
8 │     use suitrump_dex::pair::{Self, AdminCap, Pair, LPCoin};
  │                                                    ^^^^^^ Unused 'use' of alias 'LPCoin'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_fund_loss_tests.move:9:30
  │
9 │     use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
  │                              ^^^^ Unused 'use' of alias 'farm'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_fund_loss_tests.move:9:36
  │
9 │     use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
  │                                    ^^^^ Unused 'use' of alias 'Farm'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_fund_loss_tests.move:9:54
  │
9 │     use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
  │                                                      ^^^^^^^^^^^^ Unused 'use' of alias 'FarmAdminCap'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_fund_loss_tests.move:9:68
  │
9 │     use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
  │                                                                    ^^^^^^^^^^^ Unused 'use' of alias 'RewardVault'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_fund_loss_tests.move:9:81
  │
9 │     use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
  │                                                                                 ^^^^^^^^^^^^^^^ Unused 'use' of alias 'StakingPosition'. Consider removing it     
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_fund_loss_tests.move:10:36
   │
10 │     use suitrump_dex::test_coins::{Self, USDC};
   │                                    ^^^^ Unused 'use' of alias 'test_coins'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_fund_loss_tests.move:11:39
   │
11 │     use suitrump_dex::victory_token::{Self, VICTORY_TOKEN, TreasuryCapWrapper};
   │                                       ^^^^ Unused 'use' of alias 'victory_token'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_fund_loss_tests.move:11:45
   │
11 │     use suitrump_dex::victory_token::{Self, VICTORY_TOKEN, TreasuryCapWrapper};
   │                                             ^^^^^^^^^^^^^ Unused 'use' of alias 'VICTORY_TOKEN'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_fund_loss_tests.move:11:60
   │
11 │     use suitrump_dex::victory_token::{Self, VICTORY_TOKEN, TreasuryCapWrapper};
   │                                                            ^^^^^^^^^^^^^^^^^^ Unused 'use' of alias 'TreasuryCapWrapper'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_fund_loss_tests.move:12:52
   │
12 │     use suitrump_dex::global_emission_controller::{Self, GlobalEmissionConfig, AdminCap as EmissionAdminCap};
   │                                                    ^^^^ Unused 'use' of alias 'global_emission_controller'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_fund_loss_tests.move:12:58
   │
12 │     use suitrump_dex::global_emission_controller::{Self, GlobalEmissionConfig, AdminCap as EmissionAdminCap};
   │                                                          ^^^^^^^^^^^^^^^^^^^^ Unused 'use' of alias 'GlobalEmissionConfig'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_fund_loss_tests.move:12:92
   │
12 │     use suitrump_dex::global_emission_controller::{Self, GlobalEmissionConfig, AdminCap as EmissionAdminCap};
   │                                                                                            ^^^^^^^^^^^^^^^^ Unused 'use' of alias 'EmissionAdminCap'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_fund_loss_tests.move:14:22
   │
14 │     use sui::clock::{Self, Clock};
   │                      ^^^^ Unused 'use' of alias 'clock'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_fund_loss_tests.move:14:28
   │
14 │     use sui::clock::{Self, Clock};
   │                            ^^^^^ Unused 'use' of alias 'Clock'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./tests/security_fund_loss_tests.move:15:14
   │
15 │     use sui::transfer;
   │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/security_fund_loss_tests.move:19:11
   │
19 │     const USER2: address = @0x3;
   │           ^^^^^ The constant 'USER2' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/suifarm.move:3:23
  │
3 │     use sui::object::{Self, UID};
  │                       ^^^^ Unnecessary alias 'object' for module 'sui::object'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/suifarm.move:3:29
  │
3 │     use sui::object::{Self, UID};
  │                             ^^^ Unnecessary alias 'UID' for module member 'sui::object::UID'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/suifarm.move:4:27
  │
4 │     use sui::tx_context::{Self, TxContext};
  │                           ^^^^ Unnecessary alias 'tx_context' for module 'sui::tx_context'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/suifarm.move:4:33
  │
4 │     use sui::tx_context::{Self, TxContext};
  │                                 ^^^^^^^^^ Unnecessary alias 'TxContext' for module member 'sui::tx_context::TxContext'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/suifarm.move:5:14
  │
5 │     use sui::transfer;
  │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/suifarm.move:9:33
  │
9 │     use sui::coin::{Self, Coin, TreasuryCap};
  │                                 ^^^^^^^^^^^ Unused 'use' of alias 'TreasuryCap'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./sources/suifarm.move:12:23
   │
12 │     use std::option::{Self, Option};
   │                       ^^^^ Unnecessary alias 'option' for module 'std::option'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./sources/suifarm.move:12:29
   │
12 │     use std::option::{Self, Option};
   │                             ^^^^^^ Unnecessary alias 'Option' for module member 'std::option::Option'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./sources/suifarm.move:13:14
   │
13 │     use std::vector;
   │              ^^^^^^ Unnecessary alias 'vector' for module 'std::vector'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./sources/suifarm.move:14:30
   │
14 │     use suitrump_dex::pair::{Self, Pair, LPCoin};
   │                              ^^^^ Unused 'use' of alias 'pair'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./sources/suifarm.move:14:36
   │
14 │     use suitrump_dex::pair::{Self, Pair, LPCoin};
   │                                    ^^^^ Unused 'use' of alias 'Pair'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./sources/suifarm.move:16:39
   │
16 │     use suitrump_dex::victory_token::{Self, VICTORY_TOKEN,TreasuryCapWrapper};
   │                                       ^^^^ Unused 'use' of alias 'victory_token'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./sources/suifarm.move:16:59
   │
16 │     use suitrump_dex::victory_token::{Self, VICTORY_TOKEN,TreasuryCapWrapper};
   │                                                           ^^^^^^^^^^^^^^^^^^ Unused 'use' of alias 'TreasuryCapWrapper'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./sources/suifarm.move:18:39
   │
18 │     use suitrump_dex::factory::{Self, Factory};
   │                                       ^^^^^^^ Unused 'use' of alias 'Factory'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/suifarm.move:22:11
   │
22 │     const ERROR_NOT_ADMIN: u64 = 1;
   │           ^^^^^^^^^^^^^^^ The constant 'ERROR_NOT_ADMIN' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/suifarm.move:34:11
   │
34 │     const ERROR_MISSING_TREASURY_CAP: u64 = 13;
   │           ^^^^^^^^^^^^^^^^^^^^^^^^^^ The constant 'ERROR_MISSING_TREASURY_CAP' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_overflow_tests.move:3:42
  │
3 │     use sui::test_scenario::{Self as ts, Scenario};
  │                                          ^^^^^^^^ Unused 'use' of alias 'Scenario'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_overflow_tests.move:4:59
  │
4 │     use suitrump_dex::fixed_point_math::{Self as fp_math, FixedPoint};
  │                                                           ^^^^^^^^^^ Unused 'use' of alias 'FixedPoint'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W10007]: issue with attribute value
   ┌─ ./tests/security_overflow_tests.move:13:24
   │
13 │     #[expected_failure(abort_code = 0)] // E_OVERFLOW
   │                        ^^^^^^^^^^^^^^
   │                        │            │
   │                        │            Replace value with a constant from expected module or add 'location=...'
   │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
   ┌─ ./tests/security_overflow_tests.move:45:24
   │
45 │     #[expected_failure(abort_code = 0)] // E_OVERFLOW
   │                        ^^^^^^^^^^^^^^
   │                        │            │
   │                        │            Replace value with a constant from expected module or add 'location=...'
   │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
   ┌─ ./tests/security_overflow_tests.move:75:24
   │
75 │     #[expected_failure(abort_code = 0)] // E_OVERFLOW
   │                        ^^^^^^^^^^^^^^
   │                        │            │
   │                        │            Replace value with a constant from expected module or add 'location=...'
   │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
    ┌─ ./tests/security_overflow_tests.move:101:24
    │
101 │     #[expected_failure(abort_code = 0)] // E_OVERFLOW
    │                        ^^^^^^^^^^^^^^
    │                        │            │
    │                        │            Replace value with a constant from expected module or add 'location=...'
    │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
    ┌─ ./tests/security_overflow_tests.move:128:24
    │
128 │     #[expected_failure(abort_code = 0)] // E_OVERFLOW
    │                        ^^^^^^^^^^^^^^
    │                        │            │
    │                        │            Replace value with a constant from expected module or add 'location=...'
    │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
    ┌─ ./tests/security_overflow_tests.move:197:24
    │
197 │     #[expected_failure(abort_code = 0)] // E_OVERFLOW
    │                        ^^^^^^^^^^^^^^
    │                        │            │
    │                        │            Replace value with a constant from expected module or add 'location=...'
    │                        WARNING: passes for an abort from any module

warning[W09001]: unused alias
  ┌─ ./sources/fixed_point_math.move:3:15
  │
3 │     use std::{u128, u256};
  │               ^^^^ Unused 'use' of alias 'u128'. Consider removing it
  │
  = This alias does not shadow the built-in type 'u128' in type annotations.
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/fixed_point_math.move:15:11
   │
15 │     const E_INVALID_CONVERSION: u64 = 2;
   │           ^^^^^^^^^^^^^^^^^^^^ The constant 'E_INVALID_CONVERSION' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09008]: unused function
   ┌─ ./sources/fixed_point_math.move:50:9
   │
50 │     fun sqrt_internal(y: u256): u256 {
   │         ^^^^^^^^^^^^^ The non-'public', non-'entry' function 'sqrt_internal' is never called. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_function)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09008]: unused function
    ┌─ ./sources/fixed_point_math.move:158:9
    │
158 │     fun abs_difference(a: u256, b: u256): u256 {
    │         ^^^^^^^^^^^^^^ The non-'public', non-'entry' function 'abs_difference' is never called. Consider removing it.
    │
    = This warning can be suppressed with '#[allow(unused_function)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02018]: unknown attribute
  ┌─ ./tests/farm_tests.move:2:3
  │
2 │ #[unused]
  │   ^^^^^^ Unknown attribute 'unused'. Custom attributes must be wrapped in 'ext', e.g. '#[ext(unused)]'

warning[W09001]: unused alias
   ┌─ ./tests/farm_tests.move:14:30
   │
14 │     use suitrump_dex::pair::{Self, LPCoin};
   │                              ^^^^ Unused 'use' of alias 'pair'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/farm_tests.move:312:48
    │
312 │             let (can_earn_rewards, allocation, status) =
    │                                                ^^^^^^ Unused local variable 'status'. Consider removing or prefixing with an underscore: '_status'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/farm_tests.move:498:55
    │
498 │             let (current_week, phase, total_emission, paused, remaining_weeks) =
    │                                                       ^^^^^^ Unused local variable 'paused'. Consider removing or prefixing with an underscore: '_paused'       
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/farm_tests.move:498:63
    │
498 │             let (current_week, phase, total_emission, paused, remaining_weeks) =
    │                                                               ^^^^^^^^^^^^^^^ Unused local variable 'remaining_weeks'. Consider removing or prefixing with an underscore: '_remaining_weeks'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/farm_tests.move:515:72
    │
515 │             let (lp_allocation, single_allocation, allocations_active, week) =
    │                                                                        ^^^^ Unused local variable 'week'. Consider removing or prefixing with an underscore: '_week'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/farm_tests.move:591:32
    │
591 │             let (total_staked, deposit_fee, withdrawal_fee, active, is_native_pair, is_lp_token) =
    │                                ^^^^^^^^^^^ Unused local variable 'deposit_fee'. Consider removing or prefixing with an underscore: '_deposit_fee'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/farm_tests.move:591:45
    │
591 │             let (total_staked, deposit_fee, withdrawal_fee, active, is_native_pair, is_lp_token) =
    │                                             ^^^^^^^^^^^^^^ Unused local variable 'withdrawal_fee'. Consider removing or prefixing with an underscore: '_withdrawal_fee'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/farm_tests.move:600:51
    │
600 │             let (can_earn_rewards, lp_allocation, status) =
    │                                                   ^^^^^^ Unused local variable 'status'. Consider removing or prefixing with an underscore: '_status'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:1911:13
     │
1911 │         let initial_burn_balance = 0u64;
     │             ^^^^^^^^^^^^^^^^^^^^ Unused local variable 'initial_burn_balance'. Consider removing or prefixing with an underscore: '_initial_burn_balance'      
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:1912:13
     │
1912 │         let initial_locker_balance = 0u64;
     │             ^^^^^^^^^^^^^^^^^^^^^^ Unused local variable 'initial_locker_balance'. Consider removing or prefixing with an underscore: '_initial_locker_balance'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:1913:13
     │
1913 │         let initial_team_balance = 0u64;
     │             ^^^^^^^^^^^^^^^^^^^^ Unused local variable 'initial_team_balance'. Consider removing or prefixing with an underscore: '_initial_team_balance'      
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:1914:13
     │
1914 │         let initial_dev_balance = 0u64;
     │             ^^^^^^^^^^^^^^^^^^^ Unused local variable 'initial_dev_balance'. Consider removing or prefixing with an underscore: '_initial_dev_balance'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:2626:17
     │
2626 │             let result = std::option::none<bool>();
     │                 ^^^^^^ Unused local variable 'result'. Consider removing or prefixing with an underscore: '_result'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:2812:18
     │
2812 │             let (total_staked, deposit_fee, withdrawal_fee, active, is_native, is_lp) =
     │                  ^^^^^^^^^^^^ Unused local variable 'total_staked'. Consider removing or prefixing with an underscore: '_total_staked'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:2812:69
     │
2812 │             let (total_staked, deposit_fee, withdrawal_fee, active, is_native, is_lp) =
     │                                                                     ^^^^^^^^^ Unused local variable 'is_native'. Consider removing or prefixing with an underscore: '_is_native'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:2812:80
     │
2812 │             let (total_staked, deposit_fee, withdrawal_fee, active, is_native, is_lp) =
     │                                                                                ^^^^^ Unused local variable 'is_lp'. Consider removing or prefixing with an underscore: '_is_lp'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:4589:53
     │
4589 │             let (usdc_has_rewards, usdc_allocation, usdc_status) =
     │                                                     ^^^^^^^^^^^ Unused local variable 'usdc_status'. Consider removing or prefixing with an underscore: '_usdc_status'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:5146:39
     │
5146 │             let (current_week, phase, total_emission, _, _) =
     │                                       ^^^^^^^^^^^^^^ Unused local variable 'total_emission'. Consider removing or prefixing with an underscore: '_total_emission'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:5158:60
     │
5158 │             let (lp_allocation, single_allocation, active, week) =
     │                                                            ^^^^ Unused local variable 'week'. Consider removing or prefixing with an underscore: '_week'       
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:5218:68
     │
5218 │             let (new_lp_allocation, new_single_allocation, active, week) =
     │                                                                    ^^^^ Unused local variable 'week'. Consider removing or prefixing with an underscore: '_week'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:5316:52
     │
5316 │             let (lp_allocation, single_allocation, active, week) =
     │                                                    ^^^^^^ Unused local variable 'active'. Consider removing or prefixing with an underscore: '_active'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:5316:60
     │
5316 │             let (lp_allocation, single_allocation, active, week) =
     │                                                            ^^^^ Unused local variable 'week'. Consider removing or prefixing with an underscore: '_week'       
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:6476:30
     │
6476 │             let (lp_per_sec, lp_per_hour, lp_per_day, lp_per_week, lp_per_month) =
     │                              ^^^^^^^^^^^ Unused local variable 'lp_per_hour'. Consider removing or prefixing with an underscore: '_lp_per_hour'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/farm_tests.move:6476:55
     │
6476 │             let (lp_per_sec, lp_per_hour, lp_per_day, lp_per_week, lp_per_month) =
     │                                                       ^^^^^^^^^^^ Unused local variable 'lp_per_week'. Consider removing or prefixing with an underscore: '_lp_per_week'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/router_tests.move:12:36
   │
12 │     use suitrump_dex::test_coins::{Self, USDC, USDT,STK1,STK5, STK10};
   │                                    ^^^^ Unused 'use' of alias 'test_coins'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/router_tests.move:12:53
   │
12 │     use suitrump_dex::test_coins::{Self, USDC, USDT,STK1,STK5, STK10};
   │                                                     ^^^^ Unused 'use' of alias 'STK1'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/router_tests.move:12:58
   │
12 │     use suitrump_dex::test_coins::{Self, USDC, USDT,STK1,STK5, STK10};
   │                                                          ^^^^ Unused 'use' of alias 'STK5'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/router_tests.move:12:64
   │
12 │     use suitrump_dex::test_coins::{Self, USDC, USDT,STK1,STK5, STK10};
   │                                                                ^^^^^ Unused 'use' of alias 'STK10'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/router_tests.move:17:11
   │
17 │     const USER: address = @0x2;
   │           ^^^^ The constant 'USER' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/router_tests.move:43:11
   │
43 │     const TRILLION_BN: u256 = 1_000_000_000_000;           // 1T tokens
   │           ^^^^^^^^^^^ The constant 'TRILLION_BN' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/router_tests.move:44:11
   │
44 │     const TEN_TRILLION_BN: u256 = 10_000_000_000_000;      // 10T tokens
   │           ^^^^^^^^^^^^^^^ The constant 'TEN_TRILLION_BN' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/router_tests.move:45:11
   │
45 │     const HUNDRED_TRILLION_BN: u256 = 100_000_000_000_000;  // 100T tokens
   │           ^^^^^^^^^^^^^^^^^^^ The constant 'HUNDRED_TRILLION_BN' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/router_tests.move:46:11
   │
46 │     const FIFTY_TRILLION_BN: u256 = 50_000_000_000_000;     // 50T tokens
   │           ^^^^^^^^^^^^^^^^^ The constant 'FIFTY_TRILLION_BN' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/router_tests.move:48:11
   │
48 │     const TOKEN_DECIMALS: u8 = 6;  // Both USDC and USDT typically use 6 decimals
   │           ^^^^^^^^^^^^^^ The constant 'TOKEN_DECIMALS' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/router_tests.move:49:11
   │
49 │     const INITIAL_PRICE_USDC: u64 = 1_000_000;  // $1.00 with 6 decimals
   │           ^^^^^^^^^^^^^^^^^^ The constant 'INITIAL_PRICE_USDC' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/router_tests.move:50:11
   │
50 │     const INITIAL_PRICE_USDT: u64 = 1_000_000;  // $1.00 with 6 decimals
   │           ^^^^^^^^^^^^^^^^^^ The constant 'INITIAL_PRICE_USDT' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/router_tests.move:3450:17
     │
3450 │             let expected_locker_fee = (total_fee * 3) / 30;  // 0.03%
     │                 ^^^^^^^^^^^^^^^^^^^ Unused local variable 'expected_locker_fee'. Consider removing or prefixing with an underscore: '_expected_locker_fee'     
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/router_tests.move:3451:17
     │
3451 │             let expected_buyback_fee = (total_fee * 3) / 30; // 0.03%
     │                 ^^^^^^^^^^^^^^^^^^^^ Unused local variable 'expected_buyback_fee'. Consider removing or prefixing with an underscore: '_expected_buyback_fee'  
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09008]: unused function
     ┌─ ./tests/router_tests.move:3977:9
     │
3977 │     fun to_sui_units_bn(x: u256): u256 {
     │         ^^^^^^^^^^^^^^^ The non-'public', non-'entry' function 'to_sui_units_bn' is never called. Consider removing it.
     │
     = This warning can be suppressed with '#[allow(unused_function)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09008]: unused function
     ┌─ ./tests/router_tests.move:3981:9
     │
3981 │     fun to_usdc_units_bn(x: u256): u256 {
     │         ^^^^^^^^^^^^^^^^ The non-'public', non-'entry' function 'to_usdc_units_bn' is never called. Consider removing it.
     │
     = This warning can be suppressed with '#[allow(unused_function)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_precision_tests.move:3:42
  │
3 │     use sui::test_scenario::{Self as ts, Scenario};
  │                                          ^^^^^^^^ Unused 'use' of alias 'Scenario'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_precision_tests.move:5:59
  │
5 │     use suitrump_dex::fixed_point_math::{Self as fp_math, FixedPoint};
  │                                                           ^^^^^^^^^^ Unused 'use' of alias 'FixedPoint'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

error[E04003]: built-in operation not supported
    ┌─ ./tests/security_precision_tests.move:197:28
    │
194 │             let would_overflow = large_a > MAX_U256 / large_b;
    │                                  ---------------------------- Found: 'bool'. But expected: 'u8', 'u16', 'u32', 'u64', 'u128', 'u256'
    ·
197 │             debug::print(&(would_overflow as u256));
    │                            ^^^^^^^^^^^^^^ Invalid argument to 'as'

error[E04003]: built-in operation not supported
    ┌─ ./tests/security_precision_tests.move:212:28
    │
210 │             let would_shift_overflow = shift_test_value > (MAX_U256 >> 64);
    │                                        ----------------------------------- Found: 'bool'. But expected: 'u8', 'u16', 'u32', 'u64', 'u128', 'u256'
211 │             debug::print(&b"Would shift operation overflow?");
212 │             debug::print(&(would_shift_overflow as u256));
    │                            ^^^^^^^^^^^^^^^^^^^^ Invalid argument to 'as'

warning[W09002]: unused variable
    ┌─ ./tests/security_precision_tests.move:231:17
    │
231 │             let value_9_decimals = 1000000000u256; // 1 token with 9 decimals
    │                 ^^^^^^^^^^^^^^^^ Unused local variable 'value_9_decimals'. Consider removing or prefixing with an underscore: '_value_9_decimals'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/token_locker.move:3:23
  │
3 │     use sui::object::{Self, ID, UID};
  │                       ^^^^ Unnecessary alias 'object' for module 'sui::object'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/token_locker.move:3:29
  │
3 │     use sui::object::{Self, ID, UID};
  │                             ^^ Unnecessary alias 'ID' for module member 'sui::object::ID'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/token_locker.move:3:29
  │
3 │     use sui::object::{Self, ID, UID};
  │                             ^^ Unused 'use' of alias 'ID'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/token_locker.move:3:33
  │
3 │     use sui::object::{Self, ID, UID};
  │                                 ^^^ Unnecessary alias 'UID' for module member 'sui::object::UID'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/token_locker.move:4:14
  │
4 │     use sui::transfer;
  │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/token_locker.move:5:27
  │
5 │     use sui::tx_context::{Self, TxContext};
  │                           ^^^^ Unnecessary alias 'tx_context' for module 'sui::tx_context'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/token_locker.move:5:33
  │
5 │     use sui::tx_context::{Self, TxContext};
  │                                 ^^^^^^^^^ Unnecessary alias 'TxContext' for module member 'sui::tx_context::TxContext'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./sources/token_locker.move:13:14
   │
13 │     use std::vector;
   │              ^^^^^^ Unnecessary alias 'vector' for module 'std::vector'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./sources/token_locker.move:15:23
   │
15 │     use std::option::{Self, Option};
   │                       ^^^^ Unnecessary alias 'option' for module 'std::option'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./sources/token_locker.move:15:29
   │
15 │     use std::option::{Self, Option};
   │                             ^^^^^^ Unnecessary alias 'Option' for module member 'std::option::Option'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/token_locker.move:19:11
   │
19 │     const ENO_LOCK_PERIOD_MATCH: u64 = 1;
   │           ^^^^^^^^^^^^^^^^^^^^^ The constant 'ENO_LOCK_PERIOD_MATCH' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/token_locker.move:24:11
   │
24 │     const E_NOT_AUTHORIZED: u64 = 6;
   │           ^^^^^^^^^^^^^^^^ The constant 'E_NOT_AUTHORIZED' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/token_locker.move:47:11
   │
47 │     const E_VAULT_BALANCE_MISMATCH: u64 = 27;
   │           ^^^^^^^^^^^^^^^^^^^^^^^^ The constant 'E_VAULT_BALANCE_MISMATCH' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09008]: unused function
    ┌─ ./sources/token_locker.move:420:9
    │
420 │     fun validate_balance_integrity(
    │         ^^^^^^^^^^^^^^^^^^^^^^^^^^ The non-'public', non-'entry' function 'validate_balance_integrity' is never called. Consider removing it.
    │
    = This warning can be suppressed with '#[allow(unused_function)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09008]: unused function
     ┌─ ./sources/token_locker.move:1308:9
     │
1308 │     fun get_dynamic_sui_allocation(locker: &TokenLocker, lock_period: u64): u64 {
     │         ^^^^^^^^^^^^^^^^^^^^^^^^^^ The non-'public', non-'entry' function 'get_dynamic_sui_allocation' is never called. Consider removing it.
     │
     = This warning can be suppressed with '#[allow(unused_function)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_access_control_tests.move:4:21
  │
4 │     use sui::coin::{Self, mint_for_testing};
  │                     ^^^^ Unused 'use' of alias 'coin'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_access_control_tests.move:8:68
  │
8 │     use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault};
  │                                                                    ^^^^^^^^^^^ Unused 'use' of alias 'RewardVault'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_access_control_tests.move:9:36
  │
9 │     use suitrump_dex::test_coins::{Self, USDC};
  │                                    ^^^^ Unused 'use' of alias 'test_coins'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_access_control_tests.move:10:45
   │
10 │     use suitrump_dex::victory_token::{Self, VICTORY_TOKEN, TreasuryCapWrapper};
   │                                             ^^^^^^^^^^^^^ Unused 'use' of alias 'VICTORY_TOKEN'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_access_control_tests.move:10:60
   │
10 │     use suitrump_dex::victory_token::{Self, VICTORY_TOKEN, TreasuryCapWrapper};
   │                                                            ^^^^^^^^^^^^^^^^^^ Unused 'use' of alias 'TreasuryCapWrapper'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./tests/security_access_control_tests.move:13:14
   │
13 │     use sui::transfer;
   │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W04028]: implicit copy of a constant
    ┌─ ./tests/security_access_control_tests.move:135:27
    │
135 │             debug::print(&MALICIOUS_ADDRESS);
    │                           ^^^^^^^^^^^^^^^^^ This access will make a new copy of the constant. Consider binding the value to a variable first to make this copy explicit
    │
    = This warning can be suppressed with '#[allow(implicit_const_copy)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W04028]: implicit copy of a constant
    ┌─ ./tests/security_access_control_tests.move:137:27
    │
137 │             debug::print(&MALICIOUS_ADDRESS);
    │                           ^^^^^^^^^^^^^^^^^ This access will make a new copy of the constant. Consider binding the value to a variable first to make this copy explicit
    │
    = This warning can be suppressed with '#[allow(implicit_const_copy)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W04028]: implicit copy of a constant
    ┌─ ./tests/security_access_control_tests.move:139:27
    │
139 │             debug::print(&MALICIOUS_ADDRESS);
    │                           ^^^^^^^^^^^^^^^^^ This access will make a new copy of the constant. Consider binding the value to a variable first to make this copy explicit
    │
    = This warning can be suppressed with '#[allow(implicit_const_copy)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W04028]: implicit copy of a constant
    ┌─ ./tests/security_access_control_tests.move:141:27
    │
141 │             debug::print(&MALICIOUS_ADDRESS);
    │                           ^^^^^^^^^^^^^^^^^ This access will make a new copy of the constant. Consider binding the value to a variable first to make this copy explicit
    │
    = This warning can be suppressed with '#[allow(implicit_const_copy)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W04028]: implicit copy of a constant
    ┌─ ./tests/security_access_control_tests.move:180:27
    │
180 │             debug::print(&MALICIOUS_ADDRESS);
    │                           ^^^^^^^^^^^^^^^^^ This access will make a new copy of the constant. Consider binding the value to a variable first to make this copy explicit
    │
    = This warning can be suppressed with '#[allow(implicit_const_copy)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W04028]: implicit copy of a constant
    ┌─ ./tests/security_access_control_tests.move:273:27
    │
273 │             debug::print(&ATTACKER);
    │                           ^^^^^^^^ This access will make a new copy of the constant. Consider binding the value to a variable first to make this copy explicit  
    │
    = This warning can be suppressed with '#[allow(implicit_const_copy)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./tests/factory_tests.move:5:14
  │
5 │     use std::option;
  │              ^^^^^^ Unnecessary alias 'option' for module 'std::option'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/factory_tests.move:9:36
  │
9 │     use suitrump_dex::test_coins::{Self, USDC};
  │                                    ^^^^ Unused 'use' of alias 'test_coins'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/factory_tests.move:13:11
   │
13 │     const TEAM: address = @0x44;
   │           ^^^^ The constant 'TEAM' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/factory_tests.move:14:11
   │
14 │     const LOCKER: address = @0x45;
   │           ^^^^^^ The constant 'LOCKER' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/factory_tests.move:15:11
   │
15 │     const BUYBACK: address = @0x46;
   │           ^^^^^^^ The constant 'BUYBACK' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/factory_tests.move:18:11
   │
18 │     const BILLION: u128 = 1_000_000_000;
   │           ^^^^^^^ The constant 'BILLION' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/factory_tests.move:19:11
   │
19 │     const TRILLION: u128 = 1_000_000_000_000;
   │           ^^^^^^^^ The constant 'TRILLION' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W10007]: issue with attribute value
    ┌─ ./tests/factory_tests.move:122:24
    │
122 │     #[expected_failure(abort_code = 1)]
    │                        ^^^^^^^^^^^^^^
    │                        │            │
    │                        │            Replace value with a constant from expected module or add 'location=...'
    │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
    ┌─ ./tests/factory_tests.move:146:24
    │
146 │     #[expected_failure(abort_code = 2)]
    │                        ^^^^^^^^^^^^^^
    │                        │            │
    │                        │            Replace value with a constant from expected module or add 'location=...'
    │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
    ┌─ ./tests/factory_tests.move:179:24
    │
179 │     #[expected_failure(abort_code = 2)]
    │                        ^^^^^^^^^^^^^^
    │                        │            │
    │                        │            Replace value with a constant from expected module or add 'location=...'
    │                        WARNING: passes for an abort from any module

warning[W02021]: duplicate alias
  ┌─ ./sources/factory.move:3:23
  │
3 │     use sui::object::{Self, UID};
  │                       ^^^^ Unnecessary alias 'object' for module 'sui::object'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/factory.move:3:29
  │
3 │     use sui::object::{Self, UID};
  │                             ^^^ Unnecessary alias 'UID' for module member 'sui::object::UID'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/factory.move:4:27
  │
4 │     use sui::tx_context::{Self, TxContext};
  │                           ^^^^ Unnecessary alias 'tx_context' for module 'sui::tx_context'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/factory.move:4:33
  │
4 │     use sui::tx_context::{Self, TxContext};
  │                                 ^^^^^^^^^ Unnecessary alias 'TxContext' for module member 'sui::tx_context::TxContext'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/factory.move:5:14
  │
5 │     use sui::transfer;
  │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./sources/factory.move:10:23
   │
10 │     use std::option::{Self, Option};
   │                       ^^^^ Unnecessary alias 'option' for module 'std::option'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./sources/factory.move:10:29
   │
10 │     use std::option::{Self, Option};
   │                             ^^^^^^ Unnecessary alias 'Option' for module member 'std::option::Option'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./sources/factory.move:11:36
   │
11 │     use suitrump_dex::pair::{Self, Pair, AdminCap};
   │                                    ^^^^ Unused 'use' of alias 'Pair'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./sources/factory.move:11:42
   │
11 │     use suitrump_dex::pair::{Self, Pair, AdminCap};
   │                                          ^^^^^^^^ Unused 'use' of alias 'AdminCap'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./sources/factory.move:12:14
   │
12 │     use std::vector;
   │              ^^^^^^ Unnecessary alias 'vector' for module 'std::vector'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./sources/factory.move:14:48
   │
14 │     use suitrump_dex::fixed_point_math::{Self, FixedPoint};
   │                                                ^^^^^^^^^^ Unused 'use' of alias 'FixedPoint'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/factory.move:26:11
   │
26 │     const PRECISION: u256 = 10000; // Fee precision (basis points)
   │           ^^^^^^^^^ The constant 'PRECISION' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/library_tests.move:3:26
  │
3 │     use sui::test_utils::assert_eq;
  │                          ^^^^^^^^^ Unused 'use' of alias 'assert_eq'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/library_tests.move:17:11
   │
17 │     const QUADRILLION: u256 = 1_000_000_000_000_000;
   │           ^^^^^^^^^^^ The constant 'QUADRILLION' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/library_tests.move:21:11
   │
21 │     const MAX_U256: u256 = 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff;
   │           ^^^^^^^^ The constant 'MAX_U256' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/library_tests.move:22:11
   │
22 │     const HALF_MAX_U256: u256 = 0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff;
   │           ^^^^^^^^^^^^^ The constant 'HALF_MAX_U256' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./tests/pair_tests.move:7:14
  │
7 │     use std::option;
  │              ^^^^^^ Unnecessary alias 'option' for module 'std::option'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/pair_tests.move:9:52
  │
9 │     use suitrump_dex::pair::{Self, AdminCap, Pair, LPCoin};
  │                                                    ^^^^^^ Unused 'use' of alias 'LPCoin'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/pair_tests.move:10:36
   │
10 │     use suitrump_dex::test_coins::{Self, USDC};
   │                                    ^^^^ Unused 'use' of alias 'test_coins'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./tests/pair_tests.move:12:14
   │
12 │     use sui::transfer;
   │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/pair_tests.move:13:29
   │
13 │     use std::string::{Self, String};
   │                             ^^^^^^ Unused 'use' of alias 'String'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/pair_tests.move:23:11
   │
23 │     const MILLION: u64 = 1_000_000;
   │           ^^^^^^^ The constant 'MILLION' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/pair_tests.move:27:11
   │
27 │     const MINIMUM_LIQUIDITY: u128 = 1000;
   │           ^^^^^^^^^^^^^^^^^ The constant 'MINIMUM_LIQUIDITY' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09008]: unused function
    ┌─ ./tests/pair_tests.move:724:5
    │
724 │ fun calculate_percentage(value: u64, numerator: u64, denominator: u64): u64 {
    │     ^^^^^^^^^^^^^^^^^^^^ The non-'public', non-'entry' function 'calculate_percentage' is never called. Consider removing it.
    │
    = This warning can be suppressed with '#[allow(unused_function)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/global_emission_controller.move:3:23
  │
3 │     use sui::object::{Self, UID};
  │                       ^^^^ Unnecessary alias 'object' for module 'sui::object'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/global_emission_controller.move:3:29
  │
3 │     use sui::object::{Self, UID};
  │                             ^^^ Unnecessary alias 'UID' for module member 'sui::object::UID'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/global_emission_controller.move:4:14
  │
4 │     use sui::transfer;
  │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/global_emission_controller.move:5:27
  │
5 │     use sui::tx_context::{Self, TxContext};
  │                           ^^^^ Unnecessary alias 'tx_context' for module 'sui::tx_context'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/global_emission_controller.move:5:33
  │
5 │     use sui::tx_context::{Self, TxContext};
  │                                 ^^^^^^^^^ Unnecessary alias 'TxContext' for module member 'sui::tx_context::TxContext'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/router.move:3:23
  │
3 │     use sui::object::{Self, UID};
  │                       ^^^^ Unnecessary alias 'object' for module 'sui::object'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/router.move:3:29
  │
3 │     use sui::object::{Self, UID};
  │                             ^^^ Unnecessary alias 'UID' for module member 'sui::object::UID'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/router.move:4:27
  │
4 │     use sui::tx_context::{Self, TxContext};
  │                           ^^^^ Unnecessary alias 'tx_context' for module 'sui::tx_context'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/router.move:4:33
  │
4 │     use sui::tx_context::{Self, TxContext};
  │                                 ^^^^^^^^^ Unnecessary alias 'TxContext' for module member 'sui::tx_context::TxContext'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/router.move:5:14
  │
5 │     use sui::transfer;
  │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/router.move:7:23
  │
7 │     use std::option::{Self, Option};
  │                       ^^^^ Unnecessary alias 'option' for module 'std::option'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/router.move:7:29
  │
7 │     use std::option::{Self, Option};
  │                             ^^^^^^ Unnecessary alias 'Option' for module member 'std::option::Option'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/router.move:22:11
   │
22 │     const ERR_INVALID_PATH: u64 = 307;
   │           ^^^^^^^^^^^^^^^^ The constant 'ERR_INVALID_PATH' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/router.move:26:11
   │
26 │     const ERR_SLIPPAGE_EXCEEDED: u64 = 312;
   │           ^^^^^^^^^^^^^^^^^^^^^ The constant 'ERR_SLIPPAGE_EXCEEDED' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/router.move:30:11
   │
30 │     const BASIS_POINTS: u256 = 10000;
   │           ^^^^^^^^^^^^ The constant 'BASIS_POINTS' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./tests/test_coins.move:3:14
  │
3 │     use std::option;
  │              ^^^^^^ Unnecessary alias 'option' for module 'std::option'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/test_coins.move:3:14
  │
3 │     use std::option;
  │              ^^^^^^ Unused 'use' of alias 'option'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/test_coins.move:4:21
  │
4 │     use sui::coin::{Self, TreasuryCap, CoinMetadata};
  │                     ^^^^ Unused 'use' of alias 'coin'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/test_coins.move:4:27
  │
4 │     use sui::coin::{Self, TreasuryCap, CoinMetadata};
  │                           ^^^^^^^^^^^ Unused 'use' of alias 'TreasuryCap'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/test_coins.move:4:40
  │
4 │     use sui::coin::{Self, TreasuryCap, CoinMetadata};
  │                                        ^^^^^^^^^^^^ Unused 'use' of alias 'CoinMetadata'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./tests/test_coins.move:5:14
  │
5 │     use sui::transfer;
  │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/test_coins.move:5:14
  │
5 │     use sui::transfer;
  │              ^^^^^^^^ Unused 'use' of alias 'transfer'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./tests/test_coins.move:6:26
  │
6 │     use sui::tx_context::TxContext;
  │                          ^^^^^^^^^ Unnecessary alias 'TxContext' for module member 'sui::tx_context::TxContext'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/test_coins.move:7:14
  │
7 │     use sui::test_utils;
  │              ^^^^^^^^^^ Unused 'use' of alias 'test_utils'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
   ┌─ ./tests/test_coins.move:19:33
   │
19 │     public fun init_for_testing(ctx: &mut TxContext) {}
   │                                 ^^^ Unused parameter 'ctx'. Consider removing or prefixing with an underscore: '_ctx'
   │
   = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/victorytoken.move:2:27
  │
2 │     use sui::tx_context::{Self, TxContext};
  │                           ^^^^ Unnecessary alias 'tx_context' for module 'sui::tx_context'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/victorytoken.move:2:33
  │
2 │     use sui::tx_context::{Self, TxContext};
  │                                 ^^^^^^^^^ Unnecessary alias 'TxContext' for module member 'sui::tx_context::TxContext'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/victorytoken.move:4:14
  │
4 │     use sui::transfer;
  │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/victorytoken.move:6:23
  │
6 │     use sui::object::{Self, UID};
  │                       ^^^^ Unnecessary alias 'object' for module 'sui::object'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/victorytoken.move:6:29
  │
6 │     use sui::object::{Self, UID};
  │                             ^^^ Unnecessary alias 'UID' for module member 'sui::object::UID'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/victorytoken.move:7:23
  │
7 │     use std::string::{Self, String};
  │                       ^^^^ Unused 'use' of alias 'string'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/victorytoken.move:8:14
  │
8 │     use std::option;
  │              ^^^^^^ Unnecessary alias 'option' for module 'std::option'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/fixed_point_math_tests.move:3:42
  │
3 │     use sui::test_scenario::{Self as ts, Scenario};
  │                                          ^^^^^^^^ Unused 'use' of alias 'Scenario'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/fixed_point_math_tests.move:4:59
  │
4 │     use suitrump_dex::fixed_point_math::{Self as fp_math, FixedPoint};
  │                                                           ^^^^^^^^^^ Unused 'use' of alias 'FixedPoint'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/fixed_point_math_tests.move:11:11
   │
11 │     const TRILLION: u256 = 1_000_000_000_000;
   │           ^^^^^^^^ The constant 'TRILLION' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/fixed_point_math_tests.move:19:11
   │
19 │     const BASIS_POINTS: u256 = 10000;
   │           ^^^^^^^^^^^^ The constant 'BASIS_POINTS' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/fixed_point_math_tests.move:27:11
   │
27 │     const DOGE_SUPPLY: u256 = 132_670_764_300_000;      // ~132.67 Trillion
   │           ^^^^^^^^^^^ The constant 'DOGE_SUPPLY' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/fixed_point_math_tests.move:152:17
    │
152 │             let double_quad = fp_math::from_raw(QUADRILLION / quad_div * 2, 9);
    │                 ^^^^^^^^^^^ Unused local variable 'double_quad'. Consider removing or prefixing with an underscore: '_double_quad'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09008]: unused function
    ┌─ ./tests/fixed_point_math_tests.move:269:9
    │
269 │     fun percentage_of(number: u128, percentage: u128): u128 {
    │         ^^^^^^^^^^^^^ The non-'public', non-'entry' function 'percentage_of' is never called. Consider removing it.
    │
    = This warning can be suppressed with '#[allow(unused_function)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/fixed_point_math_tests.move:684:17
    │
684 │             let expected_fee_b = fees_token_b / 2;
    │                 ^^^^^^^^^^^^^^ Unused local variable 'expected_fee_b'. Consider removing or prefixing with an underscore: '_expected_fee_b'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./sources/pair.move:3:14
  │
3 │     use std::ascii;
  │              ^^^^^ Unused 'use' of alias 'ascii'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/pair.move:6:23
  │
6 │     use sui::object::{Self, UID, ID};
  │                       ^^^^ Unnecessary alias 'object' for module 'sui::object'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/pair.move:6:29
  │
6 │     use sui::object::{Self, UID, ID};
  │                             ^^^ Unnecessary alias 'UID' for module member 'sui::object::UID'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/pair.move:6:34
  │
6 │     use sui::object::{Self, UID, ID};
  │                                  ^^ Unnecessary alias 'ID' for module member 'sui::object::ID'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/pair.move:7:27
  │
7 │     use sui::tx_context::{Self, TxContext};
  │                           ^^^^ Unnecessary alias 'tx_context' for module 'sui::tx_context'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./sources/pair.move:7:33
  │
7 │     use sui::tx_context::{Self, TxContext};
  │                                 ^^^^^^^^^ Unnecessary alias 'TxContext' for module member 'sui::tx_context::TxContext'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./sources/pair.move:10:14
   │
10 │     use sui::transfer;
   │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/pair.move:16:11
   │
16 │     const PRECISION: u256 = 1000000000000000000; // 1e18
   │           ^^^^^^^^^ The constant 'PRECISION' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/pair.move:17:11
   │
17 │     const MAX_SWAP_PERCENT: u256 = 45; // Maximum 45% of reserves can be swapped at once
   │           ^^^^^^^^^^^^^^^^ The constant 'MAX_SWAP_PERCENT' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/pair.move:18:11
   │
18 │     const MINIMUM_RESERVE_AFTER_SWAP: u256 = 1000000; // Minimum reserve that must remain after swap
   │           ^^^^^^^^^^^^^^^^^^^^^^^^^^ The constant 'MINIMUM_RESERVE_AFTER_SWAP' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/pair.move:22:11
   │
22 │     const LP_FEE: u256 = 15;    // 0.15%
   │           ^^^^^^ The constant 'LP_FEE' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/pair.move:36:11
   │
36 │     const ERR_INSUFFICIENT_INPUT_AMOUNT: u64 = 107;
   │           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ The constant 'ERR_INSUFFICIENT_INPUT_AMOUNT' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/pair.move:38:11
   │
38 │     const ERR_CALCULATION_OVERFLOW: u64 = 109;
   │           ^^^^^^^^^^^^^^^^^^^^^^^^ The constant 'ERR_CALCULATION_OVERFLOW' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./sources/pair.move:40:11
   │
40 │     const ERR_INSUFFICIENT_FEE_AMOUNT: u64 = 111;
   │           ^^^^^^^^^^^^^^^^^^^^^^^^^^^ The constant 'ERR_INSUFFICIENT_FEE_AMOUNT' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/global_emission_controller_tests.move:11:11
   │
11 │     const USER1: address = @0x2;
   │           ^^^^^ The constant 'USER1' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/global_emission_controller_tests.move:17:11
   │
17 │     const E_DECAY_CALCULATION_ERROR: u64 = 1007;
   │           ^^^^^^^^^^^^^^^^^^^^^^^^^ The constant 'E_DECAY_CALCULATION_ERROR' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09011]: unused constant
   ┌─ ./tests/global_emission_controller_tests.move:21:11
   │
21 │     const WEEK_IN_SECONDS: u64 = 604800; // 7 * 86400
   │           ^^^^^^^^^^^^^^^ The constant 'WEEK_IN_SECONDS' is never used. Consider removing it.
   │
   = This warning can be suppressed with '#[allow(unused_const)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/global_emission_controller_tests.move:339:51
    │
339 │         let (current_week, phase, total_emission, paused, remaining_weeks) = get_emission_status(&mut scenario, &clock);
    │                                                   ^^^^^^ Unused local variable 'paused'. Consider removing or prefixing with an underscore: '_paused'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/global_emission_controller_tests.move:339:59
    │
339 │         let (current_week, phase, total_emission, paused, remaining_weeks) = get_emission_status(&mut scenario, &clock);
    │                                                           ^^^^^^^^^^^^^^^ Unused local variable 'remaining_weeks'. Consider removing or prefixing with an underscore: '_remaining_weeks'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09008]: unused function
    ┌─ ./tests/global_emission_controller_tests.move:496:5
    │
496 │ fun get_expected_emission_rate_for_week(week: u64): u256 {
    │     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ The non-'public', non-'entry' function 'get_expected_emission_rate_for_week' is never called. Consider removing it.     
    │
    = This warning can be suppressed with '#[allow(unused_function)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09008]: unused function
    ┌─ ./tests/global_emission_controller_tests.move:513:5
    │
513 │ fun test_interface_functions_for_week(scenario: &mut Scenario, clock: &Clock, week: u64) {
    │     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ The non-'public', non-'entry' function 'test_interface_functions_for_week' is never called. Consider removing it.
    │
    = This warning can be suppressed with '#[allow(unused_function)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_reentrancy_tests.move:4:21
  │
4 │     use sui::coin::{Self, mint_for_testing, Coin};
  │                     ^^^^ Unused 'use' of alias 'coin'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_reentrancy_tests.move:4:45
  │
4 │     use sui::coin::{Self, mint_for_testing, Coin};
  │                                             ^^^^ Unused 'use' of alias 'Coin'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_reentrancy_tests.move:5:26
  │
5 │     use sui::test_utils::assert_eq;
  │                          ^^^^^^^^^ Unused 'use' of alias 'assert_eq'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
  ┌─ ./tests/security_reentrancy_tests.move:7:14
  │
7 │     use std::option;
  │              ^^^^^^ Unnecessary alias 'option' for module 'std::option'. This alias is provided by default
  │
  = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
  ┌─ ./tests/security_reentrancy_tests.move:9:52
  │
9 │     use suitrump_dex::pair::{Self, AdminCap, Pair, LPCoin};
  │                                                    ^^^^^^ Unused 'use' of alias 'LPCoin'. Consider removing it
  │
  = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:10:30
   │
10 │     use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
   │                              ^^^^ Unused 'use' of alias 'farm'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:10:36
   │
10 │     use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
   │                                    ^^^^ Unused 'use' of alias 'Farm'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:10:54
   │
10 │     use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
   │                                                      ^^^^^^^^^^^^ Unused 'use' of alias 'FarmAdminCap'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:10:68
   │
10 │     use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
   │                                                                    ^^^^^^^^^^^ Unused 'use' of alias 'RewardVault'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:10:81
   │
10 │     use suitrump_dex::farm::{Self, Farm, AdminCap as FarmAdminCap, RewardVault, StakingPosition};
   │                                                                                 ^^^^^^^^^^^^^^^ Unused 'use' of alias 'StakingPosition'. Consider removing it    
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:11:36
   │
11 │     use suitrump_dex::test_coins::{Self, USDC};
   │                                    ^^^^ Unused 'use' of alias 'test_coins'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:12:39
   │
12 │     use suitrump_dex::victory_token::{Self, VICTORY_TOKEN, TreasuryCapWrapper};
   │                                       ^^^^ Unused 'use' of alias 'victory_token'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:12:45
   │
12 │     use suitrump_dex::victory_token::{Self, VICTORY_TOKEN, TreasuryCapWrapper};
   │                                             ^^^^^^^^^^^^^ Unused 'use' of alias 'VICTORY_TOKEN'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:12:60
   │
12 │     use suitrump_dex::victory_token::{Self, VICTORY_TOKEN, TreasuryCapWrapper};
   │                                                            ^^^^^^^^^^^^^^^^^^ Unused 'use' of alias 'TreasuryCapWrapper'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:13:52
   │
13 │     use suitrump_dex::global_emission_controller::{Self, GlobalEmissionConfig, AdminCap as EmissionAdminCap};
   │                                                    ^^^^ Unused 'use' of alias 'global_emission_controller'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:13:58
   │
13 │     use suitrump_dex::global_emission_controller::{Self, GlobalEmissionConfig, AdminCap as EmissionAdminCap};
   │                                                          ^^^^^^^^^^^^^^^^^^^^ Unused 'use' of alias 'GlobalEmissionConfig'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:13:92
   │
13 │     use suitrump_dex::global_emission_controller::{Self, GlobalEmissionConfig, AdminCap as EmissionAdminCap};
   │                                                                                            ^^^^^^^^^^^^^^^^ Unused 'use' of alias 'EmissionAdminCap'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:14:22
   │
14 │     use sui::clock::{Self, Clock};
   │                      ^^^^ Unused 'use' of alias 'clock'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09001]: unused alias
   ┌─ ./tests/security_reentrancy_tests.move:14:28
   │
14 │     use sui::clock::{Self, Clock};
   │                            ^^^^^ Unused 'use' of alias 'Clock'. Consider removing it
   │
   = This warning can be suppressed with '#[allow(unused_use)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W02021]: duplicate alias
   ┌─ ./tests/security_reentrancy_tests.move:15:14
   │
15 │     use sui::transfer;
   │              ^^^^^^^^ Unnecessary alias 'transfer' for module 'sui::transfer'. This alias is provided by default
   │
   = This warning can be suppressed with '#[allow(duplicate_alias)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/token_locker_tests.move:219:58
    │
219 │             let (victory_allocation, allocations_active, status) =
    │                                                          ^^^^^^ Unused local variable 'status'. Consider removing or prefixing with an underscore: '_status'    
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/token_locker_tests.move:567:64
    │
567 │             let (locked_balance, total_locked, reward_balance, total_reward_tokens,
    │                                                                ^^^^^^^^^^^^^^^^^^^ Unused local variable 'total_reward_tokens'. Consider removing or prefixing with an underscore: '_total_reward_tokens'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/token_locker_tests.move:568:46
    │
568 │                  sui_balance, sui_deposited, vault_locked_amount, vault_unlocked_amount) =
    │                                              ^^^^^^^^^^^^^^^^^^^ Unused local variable 'vault_locked_amount'. Consider removing or prefixing with an underscore: '_vault_locked_amount'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/token_locker_tests.move:568:67
    │
568 │                  sui_balance, sui_deposited, vault_locked_amount, vault_unlocked_amount) =
    │                                                                   ^^^^^^^^^^^^^^^^^^^^^ Unused local variable 'vault_unlocked_amount'. Consider removing or prefixing with an underscore: '_vault_unlocked_amount'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/token_locker_tests.move:651:55
    │
651 │             let (current_week, phase, total_emission, paused, remaining_weeks) =
    │                                                       ^^^^^^ Unused local variable 'paused'. Consider removing or prefixing with an underscore: '_paused'       
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/token_locker_tests.move:651:63
    │
651 │             let (current_week, phase, total_emission, paused, remaining_weeks) =
    │                                                               ^^^^^^^^^^^^^^^ Unused local variable 'remaining_weeks'. Consider removing or prefixing with an underscore: '_remaining_weeks'
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
    ┌─ ./tests/token_locker_tests.move:667:58
    │
667 │             let (victory_allocation, allocations_active, status) =
    │                                                          ^^^^^^ Unused local variable 'status'. Consider removing or prefixing with an underscore: '_status'    
    │
    = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W10007]: issue with attribute value
    ┌─ ./tests/token_locker_tests.move:704:24
    │
704 │     #[expected_failure(abort_code = 19)] // ECLAIM_TOO_SOON
    │                        ^^^^^^^^^^^^^^^
    │                        │            │
    │                        │            Replace value with a constant from expected module or add 'location=...'
    │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
    ┌─ ./tests/token_locker_tests.move:794:24
    │
794 │     #[expected_failure(abort_code = 20)] // EVICTORY_ALLOCATION_NOT_100_PERCENT
    │                        ^^^^^^^^^^^^^^^
    │                        │            │
    │                        │            Replace value with a constant from expected module or add 'location=...'
    │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
    ┌─ ./tests/token_locker_tests.move:829:24
    │
829 │     #[expected_failure(abort_code = 9)] // EALREADY_CLAIMED
    │                        ^^^^^^^^^^^^^^
    │                        │            │
    │                        │            Replace value with a constant from expected module or add 'location=...'
    │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
     ┌─ ./tests/token_locker_tests.move:1050:24
     │
1050 │     #[expected_failure(abort_code = 7)] // E_INSUFFICIENT_REWARDS
     │                        ^^^^^^^^^^^^^^
     │                        │            │
     │                        │            Replace value with a constant from expected module or add 'location=...'
     │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
     ┌─ ./tests/token_locker_tests.move:1218:24
     │
1218 │     #[expected_failure(abort_code = 8)] // E_INVALID_LOCK_PERIOD
     │                        ^^^^^^^^^^^^^^
     │                        │            │
     │                        │            Replace value with a constant from expected module or add 'location=...'
     │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
     ┌─ ./tests/token_locker_tests.move:1263:24
     │
1263 │     #[expected_failure(abort_code = 3)] // EZERO_AMOUNT
     │                        ^^^^^^^^^^^^^^
     │                        │            │
     │                        │            Replace value with a constant from expected module or add 'location=...'
     │                        WARNING: passes for an abort from any module

warning[W10007]: issue with attribute value
     ┌─ ./tests/token_locker_tests.move:1306:24
     │
1306 │     #[expected_failure(abort_code = 2)] // ELOCK_NOT_EXPIRED
     │                        ^^^^^^^^^^^^^^
     │                        │            │
     │                        │            Replace value with a constant from expected module or add 'location=...'
     │                        WARNING: passes for an abort from any module

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1453:18
     │
1453 │             let (week_total, three_month_total, year_total, three_year_total, total_locked) =
     │                  ^^^^^^^^^^ Unused local variable 'week_total'. Consider removing or prefixing with an underscore: '_week_total'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1453:30
     │
1453 │             let (week_total, three_month_total, year_total, three_year_total, total_locked) =
     │                              ^^^^^^^^^^^^^^^^^ Unused local variable 'three_month_total'. Consider removing or prefixing with an underscore: '_three_month_total'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1453:61
     │
1453 │             let (week_total, three_month_total, year_total, three_year_total, total_locked) =
     │                                                             ^^^^^^^^^^^^^^^^ Unused local variable 'three_year_total'. Consider removing or prefixing with an underscore: '_three_year_total'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1564:18
     │
1564 │             let (user1_week, user1_three_month, user1_year, user1_three_year, user1_total) =
     │                  ^^^^^^^^^^ Unused local variable 'user1_week'. Consider removing or prefixing with an underscore: '_user1_week'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1564:30
     │
1564 │             let (user1_week, user1_three_month, user1_year, user1_three_year, user1_total) =
     │                              ^^^^^^^^^^^^^^^^^ Unused local variable 'user1_three_month'. Consider removing or prefixing with an underscore: '_user1_three_month'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1564:61
     │
1564 │             let (user1_week, user1_three_month, user1_year, user1_three_year, user1_total) =
     │                                                             ^^^^^^^^^^^^^^^^ Unused local variable 'user1_three_year'. Consider removing or prefixing with an underscore: '_user1_three_year'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1567:18
     │
1567 │             let (user2_week, user2_three_month, user2_year, user2_three_year, user2_total) =
     │                  ^^^^^^^^^^ Unused local variable 'user2_week'. Consider removing or prefixing with an underscore: '_user2_week'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1567:30
     │
1567 │             let (user2_week, user2_three_month, user2_year, user2_three_year, user2_total) =
     │                              ^^^^^^^^^^^^^^^^^ Unused local variable 'user2_three_month'. Consider removing or prefixing with an underscore: '_user2_three_month'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1567:61
     │
1567 │             let (user2_week, user2_three_month, user2_year, user2_three_year, user2_total) =
     │                                                             ^^^^^^^^^^^^^^^^ Unused local variable 'user2_three_year'. Consider removing or prefixing with an underscore: '_user2_three_year'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1612:55
     │
1612 │             let (current_week, phase, total_emission, paused, remaining_weeks) =
     │                                                       ^^^^^^ Unused local variable 'paused'. Consider removing or prefixing with an underscore: '_paused'      
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1612:63
     │
1612 │             let (current_week, phase, total_emission, paused, remaining_weeks) =
     │                                                               ^^^^^^^^^^^^^^^ Unused local variable 'remaining_weeks'. Consider removing or prefixing with an underscore: '_remaining_weeks'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1666:39
     │
1666 │             let (current_week, phase, total_emission, _, _) =
     │                                       ^^^^^^^^^^^^^^ Unused local variable 'total_emission'. Consider removing or prefixing with an underscore: '_total_emission'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1775:39
     │
1775 │             let (current_week, phase, total_emission, _, remaining_weeks) =
     │                                       ^^^^^^^^^^^^^^ Unused local variable 'total_emission'. Consider removing or prefixing with an underscore: '_total_emission'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1792:58
     │
1792 │             let (victory_allocation, allocations_active, status) =
     │                                                          ^^^^^^ Unused local variable 'status'. Consider removing or prefixing with an underscore: '_status'   
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:1883:58
     │
1883 │             let (current_epoch_id, week_start, week_end, is_claimable, allocations_finalized) =
     │                                                          ^^^^^^^^^^^^ Unused local variable 'is_claimable'. Consider removing or prefixing with an underscore: '_is_claimable'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2177:54
     │
2177 │             let (vault_balance, vault_locked_amount, vault_unlocked_amount, lock_count, unlock_count) =
     │                                                      ^^^^^^^^^^^^^^^^^^^^^ Unused local variable 'vault_unlocked_amount'. Consider removing or prefixing with an underscore: '_vault_unlocked_amount'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2309:61
     │
2309 │             let (week_total, three_month_total, year_total, three_year_total, total_locked) =
     │                                                             ^^^^^^^^^^^^^^^^ Unused local variable 'three_year_total'. Consider removing or prefixing with an underscore: '_three_year_total'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2312:33
     │
2312 │             let (vault_balance, vault_locked_amount, vault_unlocked_amount, lock_count, unlock_count) =
     │                                 ^^^^^^^^^^^^^^^^^^^ Unused local variable 'vault_locked_amount'. Consider removing or prefixing with an underscore: '_vault_locked_amount'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W10007]: issue with attribute value
     ┌─ ./tests/token_locker_tests.move:2358:24
     │
2358 │     #[expected_failure(abort_code = 19)] // ECLAIM_TOO_SOON
     │                        ^^^^^^^^^^^^^^^
     │                        │            │
     │                        │            Replace value with a constant from expected module or add 'location=...'
     │                        WARNING: passes for an abort from any module

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2541:33
     │
2541 │             let (vault_balance, vault_locked_amount, vault_unlocked_amount, lock_count, unlock_count) =
     │                                 ^^^^^^^^^^^^^^^^^^^ Unused local variable 'vault_locked_amount'. Consider removing or prefixing with an underscore: '_vault_locked_amount'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2541:54
     │
2541 │             let (vault_balance, vault_locked_amount, vault_unlocked_amount, lock_count, unlock_count) =
     │                                                      ^^^^^^^^^^^^^^^^^^^^^ Unused local variable 'vault_unlocked_amount'. Consider removing or prefixing with an underscore: '_vault_unlocked_amount'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2541:89
     │
2541 │             let (vault_balance, vault_locked_amount, vault_unlocked_amount, lock_count, unlock_count) =
     │                                                                                         ^^^^^^^^^^^^ Unused local variable 'unlock_count'. Consider removing or prefixing with an underscore: '_unlock_count'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2740:18
     │
2740 │             let (week_victory, three_month_victory, year_victory, three_year_victory, victory_total) =
     │                  ^^^^^^^^^^^^ Unused local variable 'week_victory'. Consider removing or prefixing with an underscore: '_week_victory'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2740:32
     │
2740 │             let (week_victory, three_month_victory, year_victory, three_year_victory, victory_total) =
     │                                ^^^^^^^^^^^^^^^^^^^ Unused local variable 'three_month_victory'. Consider removing or prefixing with an underscore: '_three_month_victory'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2740:53
     │
2740 │             let (week_victory, three_month_victory, year_victory, three_year_victory, victory_total) =
     │                                                     ^^^^^^^^^^^^ Unused local variable 'year_victory'. Consider removing or prefixing with an underscore: '_year_victory'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2740:67
     │
2740 │             let (week_victory, three_month_victory, year_victory, three_year_victory, victory_total) =
     │                                                                   ^^^^^^^^^^^^^^^^^^ Unused local variable 'three_year_victory'. Consider removing or prefixing with an underscore: '_three_year_victory'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2794:52
     │
2794 │             let (current_balance, total_deposited, total_distributed) =
     │                                                    ^^^^^^^^^^^^^^^^^ Unused local variable 'total_distributed'. Consider removing or prefixing with an underscore: '_total_distributed'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2848:18
     │
2848 │                  sui_balance, sui_deposited, vault_locked_amount, vault_unlocked_amount) =
     │                  ^^^^^^^^^^^ Unused local variable 'sui_balance'. Consider removing or prefixing with an underscore: '_sui_balance'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2848:31
     │
2848 │                  sui_balance, sui_deposited, vault_locked_amount, vault_unlocked_amount) =
     │                               ^^^^^^^^^^^^^ Unused local variable 'sui_deposited'. Consider removing or prefixing with an underscore: '_sui_deposited'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2848:67
     │
2848 │                  sui_balance, sui_deposited, vault_locked_amount, vault_unlocked_amount) =
     │                                                                   ^^^^^^^^^^^^^^^^^^^^^ Unused local variable 'vault_unlocked_amount'. Consider removing or prefixing with an underscore: '_vault_unlocked_amount'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:2865:44
     │
2865 │             let (victory_valid, sui_valid, status) = victory_token_locker::validate_all_allocations(&locker);
     │                                            ^^^^^^ Unused local variable 'status'. Consider removing or prefixing with an underscore: '_status'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:3348:40
     │
3348 │             let (vault_balance_before, vault_locked_before, vault_unlocked_before, lock_count_before, unlock_count_before) =
     │                                        ^^^^^^^^^^^^^^^^^^^ Unused local variable 'vault_locked_before'. Consider removing or prefixing with an underscore: '_vault_locked_before'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:3348:84
     │
3348 │             let (vault_balance_before, vault_locked_before, vault_unlocked_before, lock_count_before, unlock_count_before) =
     │                                                                                    ^^^^^^^^^^^^^^^^^ Unused local variable 'lock_count_before'. Consider removing or prefixing with an underscore: '_lock_count_before'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:3348:103
     │
3348 │             let (vault_balance_before, vault_locked_before, vault_unlocked_before, lock_count_before, unlock_count_before) =
     │                                                                                                       ^^^^^^^^^^^^^^^^^^^ Unused local variable 'unlock_count_before'. Consider removing or prefixing with an underscore: '_unlock_count_before'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:3351:46
     │
3351 │             let (week_total_before, _, _, _, total_locked_before) =
     │                                              ^^^^^^^^^^^^^^^^^^^ Unused local variable 'total_locked_before'. Consider removing or prefixing with an underscore: '_total_locked_before'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:3371:39
     │
3371 │             let (vault_balance_after, vault_locked_after, vault_unlocked_after, lock_count_after, unlock_count_after) =
     │                                       ^^^^^^^^^^^^^^^^^^ Unused local variable 'vault_locked_after'. Consider removing or prefixing with an underscore: '_vault_locked_after'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

warning[W09002]: unused variable
     ┌─ ./tests/token_locker_tests.move:3371:81
     │
3371 │             let (vault_balance_after, vault_locked_after, vault_unlocked_after, lock_count_after, unlock_count_after) =
     │                                                                                 ^^^^^^^^^^^^^^^^ Unused local variable 'lock_count_after'. Consider removing or prefixing with an underscore: '_lock_count_after'
     │
     = This warning can be suppressed with '#[allow(unused_variable)]' applied to the 'module' or module member ('const', 'fun', or 'struct')

arusinov@DESKTOP-H0OO4JE:/mnt/c/Development/move/source-code/suidex_contract$ ^C